package com.cosfo.manage.tenant.strategy.impl;

import com.cosfo.manage.common.context.TenantConfigStatusEnum;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.tenant.model.po.TenantMeasureItemResult;
import com.cosfo.manage.tenant.strategy.TenantMeasureItemStrategy;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.req.OrderAfterSaleCountReq;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-11-13
 **/
@Service
public class MeasureItemWaitAuditAfterSaleStrategy implements TenantMeasureItemStrategy {
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    @Override
    public void doMeasureItem(TenantMeasureItemResult result) {
        OrderAfterSaleCountReq req = new OrderAfterSaleCountReq();
        req.setTenantId(result.getTenantId());
        req.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.UNAUDITED.getValue()));
        Integer waitAuditAfterSaleCounts = orderAfterSaleQueryFacade.countOrderAfterSale(req);
        String itemResult = String.format("%s（%s）", result.getItemTitle(), waitAuditAfterSaleCounts);
        result.setItemResult(itemResult);
        result.setItemResultState(waitAuditAfterSaleCounts > 0 ? TenantConfigStatusEnum.ABNORMAL.getStatus() : TenantConfigStatusEnum.NORMAL.getStatus());
    }
}
