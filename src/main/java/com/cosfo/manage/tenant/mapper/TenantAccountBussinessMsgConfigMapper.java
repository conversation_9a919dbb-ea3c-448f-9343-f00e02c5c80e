package com.cosfo.manage.tenant.mapper;

import com.cosfo.manage.merchant.model.dto.TenantAccountMsgConfigDTO;
import com.cosfo.manage.tenant.model.dto.BussinessMsgPageQueryDTO;
import com.cosfo.manage.tenant.model.dto.TenantAccountBussinessMsgQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * @Entity com.cosfo.manage.tenant.model.po.TenantAccountBussinessMsgConfig
 */
public interface TenantAccountBussinessMsgConfigMapper extends BaseMapper<TenantAccountBussinessMsgConfig> {

//    /**
//     * 根据条件连表查询
//     * @return
//     */
//    List<TenantAccountMsgConfigDTO> listWithReceive(BussinessMsgPageQueryDTO bussinessMsgPageQueryDTO);
}




