package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.manage.bill.model.dto.FinancialBillRuleDTO;
import com.cosfo.manage.bill.service.FinancialBillRuleService;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.SmsConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.AuditFlagEnum;
import com.cosfo.manage.common.context.BillPermissionTypeEnum;
import com.cosfo.manage.common.context.BillSwitchEnum;
import com.cosfo.manage.common.context.DefaultFlagEnum;
import com.cosfo.manage.common.context.DefaultTypeEnum;
import com.cosfo.manage.common.context.DocCodeChannelTypeEnum;
import com.cosfo.manage.common.context.DocCodeTargetTypeEnum;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.MerchantAccountTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreAccountDeleteFlagEnum;
import com.cosfo.manage.common.context.MerchantStoreBalanceEnums;
import com.cosfo.manage.common.context.MerchantStoreEnum;
import com.cosfo.manage.common.context.OnlinePaymentEnum;
import com.cosfo.manage.common.context.RedisKeyEnum;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.context.TenantStoreConfigEnum;
import com.cosfo.manage.common.context.auth.AuthTypeEnum;
import com.cosfo.manage.common.context.store.balance.BalanceAuthorityTypeEnum;
import com.cosfo.manage.common.context.store.balance.BalancePermissionTypeEnum;
import com.cosfo.manage.common.context.store.balance.EnableOfflinePaymentEnum;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.converter.DocCodeMappingConverter;
import com.cosfo.manage.common.model.dto.DocCodeMappingDTO;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.service.DocCodeMappingService;
import com.cosfo.manage.common.sms.model.Sms;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.LocalDateTimeUtil;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.PasswordUtil;
import com.cosfo.manage.common.util.RedisUtils;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.facade.AuthUserFacade;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantAddressFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreGroupFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.merchant.convert.MerchantAddressMapperConvert;
import com.cosfo.manage.merchant.convert.MerchantStoreMapperConvert;
import com.cosfo.manage.merchant.mapper.MerchantAddressMapper;
import com.cosfo.manage.merchant.mapper.MerchantStoreBalanceMapper;
import com.cosfo.manage.merchant.mapper.MerchantStoreMapper;
import com.cosfo.manage.merchant.model.dto.AuthQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantContactDTO;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryAddressResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAddressDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreExcelDataDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreGroupQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.dto.StoreCodeMappingDTO;
import com.cosfo.manage.merchant.model.dto.UpdateStoreInfoReqDTO;
import com.cosfo.manage.merchant.model.po.MerchantAddress;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalance;
import com.cosfo.manage.merchant.model.po.MerchantStoreGroup;
import com.cosfo.manage.merchant.model.vo.MerchantStoreVO;
import com.cosfo.manage.merchant.service.MerchantAddressService;
import com.cosfo.manage.merchant.service.MerchantBizLogService;
import com.cosfo.manage.merchant.service.MerchantContactService;
import com.cosfo.manage.merchant.service.MerchantStoreAccountService;
import com.cosfo.manage.merchant.service.MerchantStoreGroupMappingService;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.msg.service.NoticeService;
import com.cosfo.manage.product.model.vo.ProductPricingSupplyCityMappingDTO;
import com.cosfo.manage.product.service.LocationCityService;
import com.cosfo.manage.product.service.ProductPricingSupplyService;
import com.cosfo.manage.system.model.dto.CommonLocationCityDTO;
import com.cosfo.manage.system.model.po.SystemParameters;
import com.cosfo.manage.system.service.SystemParametersService;
import com.cosfo.manage.tenant.model.dto.TenantDTO;
import com.cosfo.manage.tenant.model.vo.TenantNumberCommonConfigVO;
import com.cosfo.manage.tenant.model.vo.TenantStoreConfigVO;
import com.cosfo.manage.tenant.service.AuthMenuPurviewService;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.cosfo.manage.tenant.service.TenantService;
import com.cosfo.manage.tenant.service.TenantStoreConfigService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.authentication.client.input.user.AuthUserPasswordUpdateInput;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.CallerException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.log.config.BizLogRecordContext;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreBatchImportResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.regional.resp.RegionalOrganizationResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/23 10:27
 */
@Slf4j
@Service
public class MerchantStoreServiceImpl implements MerchantStoreService {

    @Resource
    private MerchantStoreMapper merchantStoreMapper;
//    @Resource
//    private MerchantStoreAccountMapper merchantStoreAccountMapper;
//    @Resource
//    private MerchantContactMapper merchantContactMapper;
//    @Resource
//    private MerchantStoreGroupMappingRepository merchantStoreGroupMappingRepository;
//    @Resource
//    private MerchantStoreGroupRepository merchantStoreGroupRepository;
    @Resource
    private MerchantAddressMapper merchantAddressMapper;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private MerchantContactService merchantContactService;
    @Resource
    private FinancialBillRuleService financialBillRuleService;
    @Resource
    private CommonService commonService;
    @Resource
    private SystemParametersService systemParametersService;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;

    @Resource
    private MerchantStoreGroupMappingService merchantStoreGroupMappingService;
    @Resource
    private ProductPricingSupplyService productPricingSupplyService;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Lazy
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private MerchantStoreBalanceMapper merchantStoreBalanceMapper;
    @Resource
    private AuthMenuPurviewService authMenuPurviewService;
    @Resource
    private LocationCityService locationCityService;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private UserCenterMerchantAddressFacade userCenterMerchantAddressFacade;
    @Resource
    private UserCenterMerchantStoreGroupFacade userCenterMerchantStoreGroupFacade;
    @Resource
    private DocCodeMappingService docCodeMappingService;
    @Resource
    private MerchantBizLogService merchantBizLogService;
    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private TenantStoreConfigService tenantStoreConfigService;
    @Resource
    private AuthUserFacade authUserFacade;
    @Resource
    @Lazy
    private NoticeService noticeService;
    @Resource
    private MerchantDeliveryInfoService merchantDeliveryInfoService;
    @Resource
    private TenantService tenantService;

    @Override
    public PageInfo<MerchantStoreDTO> listAll(MerchantStoreQueryDTO storeQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        storeQueryDTO.setTenantId(tenantId);
        handleQueryCondition(storeQueryDTO, contextInfoDTO);
        if(storeQueryDTO.getNoMatchData()){
            return PageInfoHelper.createPageInfo(new ArrayList<>(), storeQueryDTO.getPageSize());
        }

//        PageHelper.startPage(storeQueryDTO.getPageIndex(), storeQueryDTO.getPageSize());
//        List<MerchantStoreDTO> storeDTOList = merchantStoreMapper.listAll(storeQueryDTO);
        PageInfo<MerchantStoreDTO> merchantStoreDTOPageInfo = pageList(storeQueryDTO);
        List<MerchantStoreDTO> storeDTOList = merchantStoreDTOPageInfo.getList();
        if (CollectionUtils.isEmpty(storeDTOList)) {
            return PageInfoHelper.createPageInfo(storeDTOList, storeQueryDTO.getPageSize());
        }

        List<Long> storeIds = storeDTOList.stream().map(MerchantStoreDTO::getId).collect(Collectors.toList());
        // 查询地址和联系人
        List<MerchantDeliveryAddressResultDTO> merchantAddressDTOS = selectAddressContactList(tenantId, storeIds);
//        List<MerchantDeliveryAddressResultDTO> merchantAddressDTOS = merchantAddressService.selectByStoreIds(tenantId, storeIds);
        Map<Long, MerchantDeliveryAddressResultDTO> merchantAddressDtoMap = merchantAddressDTOS.stream().collect(Collectors.toMap(MerchantDeliveryAddressResultDTO::getStoreId, item -> item, (e1, e2) -> e1));
        // 查询门店分组
        List<MerchantStoreGroupInfoDTO> merchantStoreGroupInfoDTOS = merchantStoreGroupService.batchQueryByStoreIds(tenantId, storeIds);
        Map<Long, MerchantStoreGroupInfoDTO> merchantStoreGroupInfoDTOMap = merchantStoreGroupInfoDTOS.stream().collect(Collectors.toMap(MerchantStoreGroupInfoDTO::getStoreId, item -> item));
        List<String> cityNames = new ArrayList<>(NumberConstants.TEN);
        if (!Objects.isNull(storeQueryDTO.getSkuId())) {
            List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = productPricingSupplyService.querySupplyCity(storeQueryDTO.getSkuId(), contextInfoDTO.getTenantId());
            if (!CollectionUtils.isEmpty(productPricingSupplyCityMappingDTOS)) {
                cityNames = productPricingSupplyCityMappingDTOS.stream().map(ProductPricingSupplyCityMappingDTO::getCityName).collect(Collectors.toList());
            }
        }

        // 补充 三方映射字段
        List<DocCodeMappingDTO> docCodeMappingDTOS = docCodeMappingService.selectByTargetCodeAndType(storeDTOList.stream().map(MerchantStoreDTO::getStoreNo).collect(Collectors.toList()), DocCodeTargetTypeEnum.STORE.getCode(), tenantId, null);
        Map<String, List<DocCodeMappingDTO>> docCodeMappingDTOMap = docCodeMappingDTOS.stream().collect(Collectors.groupingBy(DocCodeMappingDTO::getTargetCode));
        for (MerchantStoreDTO merchantStoreDTO : storeDTOList) {
            if (merchantAddressDtoMap.containsKey(merchantStoreDTO.getId())) {
                MerchantDeliveryAddressResultDTO merchantAddressDto = merchantAddressDtoMap.get(merchantStoreDTO.getId());
                merchantStoreDTO.setProvince(merchantAddressDto.getProvince());
                merchantStoreDTO.setCity(merchantAddressDto.getCity());
                merchantStoreDTO.setArea(merchantAddressDto.getArea());
                merchantStoreDTO.setAddress(merchantAddressDto.getAddress());
                merchantStoreDTO.setHouseNumber(Objects.isNull(merchantAddressDto.getHouseNumber()) ? "" : merchantAddressDto.getHouseNumber());
                merchantStoreDTO.setContactName(merchantAddressDto.getContactName());
                merchantStoreDTO.setDeliveryAddress(merchantAddressDto.getDeliveryAddress());
                merchantStoreDTO.setContactPhone(merchantAddressDto.getContactPhone());
            }

//            if (Objects.isNull(merchantStoreDTO.getContactName())) {
//                // 没有默认联系人设置最早的联系人
//                List<MerchantContactResultResp> contactList = merchantContactService.selectByStoreId(tenantId, merchantStoreDTO.getId());
//                if (!CollectionUtils.isEmpty(contactList)) {
//                    MerchantContactResultResp contact = contactList.get(NumberConstants.ZERO);
//                    merchantStoreDTO.setContactName(contact.getName());
//                }
//            }

            merchantStoreDTO.setSupplyStatus(cityNames.contains(merchantStoreDTO.getCity()) ? NumberConstants.ZERO : NumberConstants.ONE);

            // 门店分组
            MerchantStoreGroupInfoDTO merchantStoreGroupInfoDTO = merchantStoreGroupInfoDTOMap.get(merchantStoreDTO.getId());
            if (Objects.isNull(merchantStoreGroupInfoDTO)) {
                continue;
            }
            merchantStoreDTO.setGroupId(merchantStoreGroupInfoDTO.getMerchantStoreGroupId());
            merchantStoreDTO.setMerchantStoreGroupName(merchantStoreGroupInfoDTO.getMerchantStoreGroupName());

            if (docCodeMappingDTOMap != null && !docCodeMappingDTOMap.isEmpty()) {
                // 补充三方信息
                List<DocCodeMappingDTO> mappingDTOS = docCodeMappingDTOMap.get(merchantStoreDTO.getStoreNo());
                if (!CollectionUtils.isEmpty(mappingDTOS)) {
                    merchantStoreDTO.setThirdCodeMap(DocCodeMappingConverter.INSTANCE.toStoreCodeMappingDTOList(mappingDTOS));
                }
            }


        }
//        return PageResultDTO.success(PageInfoHelper.createPageInfo(storeDTOList, storeQueryDTO.getPageSize()));
        return merchantStoreDTOPageInfo;
    }

    private List<MerchantDeliveryAddressResultDTO> selectAddressContactList(Long tenantId, List<Long> storeIds) {
        List<MerchantAddressResultResp> merchantAddressResultResps = merchantAddressService.selectAddressListByStoreIds(tenantId, storeIds);
        if(CollectionUtil.isEmpty(merchantAddressResultResps)){
            return Collections.emptyList();
        }
        List<MerchantDeliveryAddressResultDTO> list = MerchantAddressMapperConvert.INSTANCE.respListToDtoList(merchantAddressResultResps);
        List<Long> addressIdList = list.stream().map(MerchantDeliveryAddressResultDTO::getId).collect(Collectors.toList());

        List<MerchantContactResultResp> merchantContactResultRespList = merchantContactService.selectByAddressIds(tenantId, addressIdList, null);
        Map<Long, List<MerchantContactResultResp>> contactResultRespMap = merchantContactResultRespList.stream().collect(Collectors.groupingBy(MerchantContactResultResp::getAddressId));

        for (MerchantDeliveryAddressResultDTO dto : list) {
            dto.setDeliveryAddress(org.apache.commons.lang3.StringUtils.join(dto.getProvince(), dto.getCity(), dto.getArea(), dto.getAddress(), Optional.ofNullable(dto.getHouseNumber()).orElse(org.apache.commons.lang3.StringUtils.EMPTY)));
            List<MerchantContactResultResp> resultResps = contactResultRespMap.get(dto.getId());
            if (CollectionUtils.isEmpty(resultResps)) {
                continue;
            }
            MerchantContactResultResp merchantContactResultResp = resultResps.stream()
                    .filter(resp -> DefaultFlagEnum.TURE.getFlag().equals(resp.getDefaultFlag())).findFirst().orElse(null);
            if (Objects.nonNull(merchantContactResultResp)) {
                dto.setContactName(merchantContactResultResp.getName());
                dto.setContactPhone(merchantContactResultResp.getPhone());
                continue;
            }
            MerchantContactResultResp contact = resultResps.get(NumberConstants.ZERO);
            dto.setContactName(contact.getName());
            dto.setContactPhone(contact.getPhone());
        }
        return list;
    }

    @Override
    public List<MerchantStore> listStore(MerchantStoreQueryDTO storeQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        handleQueryCondition(storeQueryDTO, contextInfoDTO);
        if(storeQueryDTO.getNoMatchData()){
            return Collections.emptyList();
        }
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setStoreName(storeQueryDTO.getStoreName());
        merchantStoreQueryReq.setType(storeQueryDTO.getType());
        merchantStoreQueryReq.setStoreId(storeQueryDTO.getId());
        merchantStoreQueryReq.setStoreIdList(storeQueryDTO.getStoreIds());
        merchantStoreQueryReq.setTenantId(storeQueryDTO.getTenantId());
        merchantStoreQueryReq.setStoreNo(storeQueryDTO.getStoreNo());
        merchantStoreQueryReq.setStatus(storeQueryDTO.getStatus());
        merchantStoreQueryReq.setStoreNoList(storeQueryDTO.getStoreNos());
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
        return MerchantStoreMapperConvert.INSTANCE.respListToMerchantStoreList(merchantStoreList);
//        return merchantStoreMapper.listStore(storeQueryDTO);
    }

    /**
     * 处理查询条件
     *
     * @param storeQueryDTO
     * @param contextInfoDTO
     */
    private void handleQueryCondition(MerchantStoreQueryDTO storeQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        storeQueryDTO.setNoMatchData(Boolean.FALSE);
        // 是否供应
        List<String> cityNames = new ArrayList<>(NumberConstants.TEN);
        if(!Objects.isNull(storeQueryDTO.getSkuId()) && !Objects.isNull(storeQueryDTO.getSupplyStatus())){
            if (!Objects.isNull(storeQueryDTO.getSkuId())) {
                List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = productPricingSupplyService.querySupplyCity(storeQueryDTO.getSkuId(), contextInfoDTO.getTenantId());
                if (!CollectionUtils.isEmpty(productPricingSupplyCityMappingDTOS)) {
                    cityNames = productPricingSupplyCityMappingDTOS.stream().map(ProductPricingSupplyCityMappingDTO::getCityName).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(cityNames)){
                        List<MerchantAddressResultResp> merchantAddresses = merchantAddressService.queryByCityList(cityNames, contextInfoDTO.getTenantId());
                        if(!CollectionUtils.isEmpty(merchantAddresses)) {
                            List<Long> storeIds = merchantAddresses.stream().map(MerchantAddressResultResp::getStoreId).collect(Collectors.toList());
                            storeQueryDTO.setSupplyStoreIds(storeIds);
                        }else {
                            if(storeQueryDTO.getSupplyStatus().equals(NumberConstant.ZERO)){
                                storeQueryDTO.setNoMatchData(Boolean.TRUE);
                                return;
                            }
                        }
                    }
                }
            }
        }

        // 门店分组列表
        List<Long> merchantStoreGroupIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(storeQueryDTO.getMerchantStoreGroupIds())) {
            merchantStoreGroupIds = storeQueryDTO.getMerchantStoreGroupIds();
        }
        if (Objects.nonNull(storeQueryDTO.getGroupId())) {
            merchantStoreGroupIds.add(storeQueryDTO.getGroupId());
        }
        if (!CollectionUtils.isEmpty(merchantStoreGroupIds)) {
            List<MerchantStoreGroupResultResp> merchantStoreGroupMappingList = merchantStoreGroupMappingService.selectByGroupId(merchantStoreGroupIds, contextInfoDTO.getTenantId());
            if(CollectionUtils.isEmpty(merchantStoreGroupMappingList)){
                storeQueryDTO.setNoMatchData(Boolean.TRUE);
                return;
            }

            List<Long> storeIds = merchantStoreGroupMappingList.stream().map(MerchantStoreGroupResultResp::getStoreId).collect(Collectors.toList());
            // 取交集
            if(!CollectionUtils.isEmpty(storeQueryDTO.getStoreIds())) {
                storeIds = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(storeIds, storeQueryDTO.getStoreIds());
            }

            if(CollectionUtils.isEmpty(storeIds)){
                storeQueryDTO.setNoMatchData(Boolean.TRUE);
                return;
            }

            storeQueryDTO.setStoreIds(storeIds);
        }

        if (!StringUtils.isEmpty(storeQueryDTO.getProvince()) || !StringUtils.isEmpty(storeQueryDTO.getCity()) || !StringUtils.isEmpty(storeQueryDTO.getArea())) {
            List<MerchantAddressResultResp> merchantAddresses = merchantAddressService.queryStore(storeQueryDTO.getProvince(), storeQueryDTO.getCity(), storeQueryDTO.getArea(), contextInfoDTO.getTenantId());
            if(!CollectionUtils.isEmpty(merchantAddresses)) {
                List<Long> storeIds = merchantAddresses.stream().map(MerchantAddressResultResp::getStoreId).collect(Collectors.toList());
                if(!Objects.isNull(storeQueryDTO.getGroupId()) && !CollectionUtils.isEmpty(storeQueryDTO.getStoreIds())){
                    // 分组绑定门店
                    List<Long> groupStoreIds = storeQueryDTO.getStoreIds();
                    // 取交集
                    List<Long> list = groupStoreIds.stream().filter(storeIds::contains).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(list)){
                        storeQueryDTO.setNoMatchData(Boolean.TRUE);
                        return;
                    }

                    // 取交集
                    if(!CollectionUtils.isEmpty(storeQueryDTO.getStoreIds())) {
                        list = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(list, storeQueryDTO.getStoreIds());
                    }

                    if(CollectionUtils.isEmpty(list)){
                        storeQueryDTO.setNoMatchData(Boolean.TRUE);
                        return;
                    }

                    storeQueryDTO.setStoreIds(list);
                }else {
                    // 取交集
                    if(!CollectionUtils.isEmpty(storeQueryDTO.getStoreIds())) {
                        storeIds = (List<Long>) org.apache.commons.collections4.CollectionUtils.intersection(storeIds, storeQueryDTO.getStoreIds());
                    }

                    if(CollectionUtils.isEmpty(storeIds)){
                        storeQueryDTO.setNoMatchData(Boolean.TRUE);
                        return;
                    }

                    storeQueryDTO.setStoreIds(storeIds);
                }
            }else {
                storeQueryDTO.setNoMatchData(Boolean.TRUE);
                return;
            }
        }

        if(!Objects.isNull(storeQueryDTO.getHavingGroup())){
            // 查询所有分组
            MerchantStoreGroupQueryDTO merchantStoreGroupQueryDto = new MerchantStoreGroupQueryDTO();
            merchantStoreGroupQueryDto.setTenantId(contextInfoDTO.getTenantId());
            List<MerchantStoreGroup> merchantStoreGroups = merchantStoreGroupService.list(merchantStoreGroupQueryDto);
//            List<MerchantStoreGroup> merchantStoreGroups = merchantStoreGroupRepository.list();
            if(!CollectionUtils.isEmpty(merchantStoreGroups)) {
                List<Long> groupIds = merchantStoreGroups.stream().map(MerchantStoreGroup::getId).collect(Collectors.toList());
                List<MerchantStoreGroupResultResp> merchantStoreGroupMappingList = merchantStoreGroupMappingService.selectByGroupId(groupIds, contextInfoDTO.getTenantId());
                List<Long> storeIds = merchantStoreGroupMappingList.stream().map(MerchantStoreGroupResultResp::getStoreId).collect(Collectors.toList());
                storeQueryDTO.setNoMatchingStoreIds(storeIds);
            }
        }
    }

    @Override
    public CommonResult export(MerchantStoreQueryDTO merchantStoreQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId();
        // 转换查询条件进行存储
        HashMap<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.TEN);
        // 门店ID
        if (Objects.nonNull(merchantStoreQueryDTO.getId())) {
            queryParamsMap.put(Constants.STORE_ID, merchantStoreQueryDTO.getId().toString());
        }
        // 门店编号
        if (Objects.nonNull(merchantStoreQueryDTO.getStoreNo())) {
            queryParamsMap.put(Constants.STORE_NO, merchantStoreQueryDTO.getStoreNo().toString());
        }
        // 下单状态
        if (Objects.nonNull(merchantStoreQueryDTO.getPlaceOrderEnableFlag ())) {
            queryParamsMap.put(Constants.PLACE_ORDER_ENABLE_FLAG, merchantStoreQueryDTO.getPlaceOrderEnableFlag ()!=null && merchantStoreQueryDTO.getPlaceOrderEnableFlag ()?"是":"否");
        }
        // 下单是否临期
        if (Objects.nonNull(merchantStoreQueryDTO.getPlaceOrderDeadlineFlag ())) {
            queryParamsMap.put(Constants.PLACE_ORDER_DEADLINE_FLAG, "是");
        }
        // 门店名称
        if (!StringUtils.isBlank(merchantStoreQueryDTO.getStoreName())) {
            queryParamsMap.put(Constants.STORE_NAME, merchantStoreQueryDTO.getStoreName());
        }
        // 手机号
        if (!StringUtils.isBlank(merchantStoreQueryDTO.getPhone())) {
            queryParamsMap.put(Constants.PHONE, merchantStoreQueryDTO.getPhone());
        }
        // 起止时间
        if (Objects.nonNull(merchantStoreQueryDTO.getStartTime()) && Objects.nonNull(merchantStoreQueryDTO.getEndTime())) {
            queryParamsMap.put(Constants.REGISTER_TIME, LocalDateTimeUtil.localTimeFormat(merchantStoreQueryDTO.getStartTime()) + StringConstants.SEPARATING_IN_LINE + LocalDateTimeUtil.localTimeFormat(merchantStoreQueryDTO.getEndTime()));
        }
        // 门店状态
        if (Objects.nonNull(merchantStoreQueryDTO.getStatus())) {
            queryParamsMap.put(Constants.STORE_STATUS, MerchantStoreEnum.QueryStatus.getDesc(merchantStoreQueryDTO.getStatus()));
        }
        // 门店类型
        if (Objects.nonNull(merchantStoreQueryDTO.getType())) {
            queryParamsMap.put(Constants.STORE_TYPE, MerchantStoreEnum.Type.getDesc(merchantStoreQueryDTO.getType()));
        }
        // 账期权限
        if (Objects.nonNull(merchantStoreQueryDTO.getBillSwitch())) {
            queryParamsMap.put(Constants.STORE_BILL_SWITCH, BillSwitchEnum.getDesc(merchantStoreQueryDTO.getBillSwitch()));
        }
        // 门店分组
        String groupNames = merchantStoreGroupService.queryGroupNameByIds(tenantId, merchantStoreQueryDTO.getMerchantStoreGroupIds());
        if (!StringUtils.isBlank(groupNames)) {
            queryParamsMap.put(Constants.STORE_GROUPS, groupNames);
        }
        // 线下支付权限
        if (Objects.nonNull(merchantStoreQueryDTO.getEnableOfflinePayment ())) {
            queryParamsMap.put(Constants.ENABLE_OFFLINE_PAYMENT, EnableOfflinePaymentEnum.getDesc(merchantStoreQueryDTO.getEnableOfflinePayment ()));
        }
        // 余额权限
        if (Objects.nonNull(merchantStoreQueryDTO.getBalanceAuthority ())) {
            queryParamsMap.put(Constants.BALANCE_AUTHORITY, BalanceAuthorityTypeEnum.getDesc(merchantStoreQueryDTO.getBalanceAuthority ()));
        }
        merchantStoreQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.STORE.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.MERCHANT_STORE.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(merchantStoreQueryDTO, e -> writeDownloadCenter(e, recordDTO.getFileName()));

        return CommonResult.ok();
    }


    public DownloadCenterOssRespDTO writeDownloadCenter(MerchantStoreQueryDTO merchantStoreQueryDTO, String fileName) {
        // 1、表格处理
        String filePath = generateStoreExportFile(merchantStoreQueryDTO);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }


    public String generateStoreExportFile(MerchantStoreQueryDTO merchantStoreQueryDTO) {
        // 处理门店分组
        List<Long> merchantStoreGroupIds = merchantStoreQueryDTO.getMerchantStoreGroupIds();
        if (!CollectionUtils.isEmpty(merchantStoreGroupIds)) {
            List<MerchantStoreGroupResultResp> merchantStoreGroupMappingList = merchantStoreGroupMappingService.selectByGroupId(merchantStoreGroupIds, merchantStoreQueryDTO.getTenantId());
            if (CollectionUtils.isEmpty(merchantStoreGroupMappingList)) {
                return commonService.exportExcel(new ArrayList(), ExcelTypeEnum.MERCHANT_STORE.getName());
            }
            List<Long> storeIds = merchantStoreGroupMappingList.stream().map(MerchantStoreGroupResultResp::getStoreId).collect(Collectors.toList());
            merchantStoreQueryDTO.setStoreIds(storeIds);
        }
        List<MerchantStoreDTO> merchantStoreDTOS = listAll(merchantStoreQueryDTO);
        if(CollectionUtils.isEmpty(merchantStoreDTOS)) {
            return commonService.exportExcel(new ArrayList(), ExcelTypeEnum.MERCHANT_STORE.getName());
        }
        List<Long> storeIds = merchantStoreDTOS.stream().map(MerchantStoreDTO::getId).collect(Collectors.toList());
        Long tenantId = merchantStoreQueryDTO.getTenantId();
        // 查询地址和联系人
        List<MerchantDeliveryAddressResultDTO> merchantAddressDTOS = selectAddressContactList(tenantId, storeIds);
        Map<Long, MerchantDeliveryAddressResultDTO> merchantAddressDtoMap = merchantAddressDTOS.stream().collect(Collectors.toMap(MerchantDeliveryAddressResultDTO::getStoreId, item -> item, (v1, v2) -> v1));
        List<MerchantStoreExcelDataDTO> excelDataDTOList = new ArrayList<>(merchantStoreDTOS.size());
        Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(merchantStoreQueryDTO.getTenantId(), storeIds);

        if (!CollectionUtils.isEmpty(merchantStoreDTOS)) {
            for (MerchantStoreDTO merchantStoreDTO : merchantStoreDTOS) {
                MerchantStoreExcelDataDTO dataDTO = new MerchantStoreExcelDataDTO();
                BeanUtils.copyProperties(merchantStoreDTO, dataDTO);
                if (merchantAddressDtoMap.containsKey(merchantStoreDTO.getId())) {
                    MerchantDeliveryAddressResultDTO merchantAddressDto = merchantAddressDtoMap.get(merchantStoreDTO.getId());
                    dataDTO.setProvince(merchantAddressDto.getProvince());
                    dataDTO.setCity(merchantAddressDto.getCity());
                    dataDTO.setArea(merchantAddressDto.getArea());
                    dataDTO.setAddress(merchantAddressDto.getAddress());
                    dataDTO.setHouseNumber(Objects.isNull(merchantAddressDto.getHouseNumber()) ? "" : merchantAddressDto.getHouseNumber());
                    dataDTO.setContactName(merchantAddressDto.getContactName());
                    dataDTO.setContactPhone(merchantAddressDto.getContactPhone());
                }
//                if (Objects.isNull(dataDTO.getContactName())) {
//                    // 没有默认联系人设置最早的联系人
//                    List<MerchantContactResultResp> contactList = merchantContactService.selectByStoreId(tenantId, merchantStoreDTO.getId());
//                    if (!CollectionUtils.isEmpty(contactList)) {
//                        MerchantContactResultResp contact = contactList.get(NumberConstants.ZERO);
//                        dataDTO.setContactName(contact.getName());
//                        dataDTO.setContactPhone(contact.getPhone());
//                    }
//                }

                dataDTO.setStoreId(merchantStoreDTO.getId());
                dataDTO.setGroupName(groupMap.get(merchantStoreDTO.getId()));
                dataDTO.setStatus(MerchantStoreEnum.Status.getDesc(merchantStoreDTO.getStatus()));
                dataDTO.setBillPermissions(BillPermissionTypeEnum.getBillPermissionDesc(merchantStoreDTO.getBillSwitch(), merchantStoreDTO.getOnlinePayment()));
                dataDTO.setBalancePermissions (BalancePermissionTypeEnum.getDesc (merchantStoreDTO.getBalanceAuthority ()));
                dataDTO.setOfflinePaymentPermissions (EnableOfflinePaymentEnum.getDesc(merchantStoreDTO.getEnableOfflinePayment ()));
                dataDTO.setTypeDesc(MerchantStoreEnum.Type.getDesc(merchantStoreDTO.getType()));
                dataDTO.setPlaceOrderEnableFlagStr(Boolean.TRUE.equals(merchantStoreDTO.getPlaceOrderEnableFlag()) ? "可下单" : "不可下单");
                dataDTO.setPlaceOrderPermissionExpiryTimeStr(Integer.valueOf(0).equals(merchantStoreDTO.getPlaceOrderPermissionTimeLimited()) ? "长期" : cn.hutool.core.date.LocalDateTimeUtil.format(merchantStoreDTO.getPlaceOrderPermissionExpiryTime(),"yyyy-MM-dd HH:mm:ss"));
                excelDataDTOList.add(dataDTO);
            }
        }

        // 数据写入excel
        return commonService.exportExcel(excelDataDTOList, ExcelTypeEnum.MERCHANT_STORE.getName());

    }

    @Override
    public MerchantStoreDTO selectDetail(Long id) {
//        MerchantStore store = merchantStoreMapper.selectByPrimaryKey(id);
        MerchantStore store = MerchantStoreMapperConvert.INSTANCE.respToMerchantStore(userCenterMerchantStoreFacade.getMerchantStoreById(id));
        if (Objects.isNull(store)) {
            throw new BizException ("门店不存在");
        }
        Long tenantId = store.getTenantId();
        if (Objects.nonNull(UserLoginContextUtils.getTenantId())) {
            AssertCheckParams.expectTrue(Objects.equals(tenantId, UserLoginContextUtils.getTenantId()), ResultStatusEnum.SERVER_ERROR.getStatus(), "请求数据不存在");
        }
        MerchantStoreDTO storeDTO = new MerchantStoreDTO();
        //门店信息
        BeanUtils.copyProperties(store, storeDTO);
        TenantResultResp tenant = userCenterTenantFacade.getTenantById(tenantId);
//        Tenant tenant = tenantMapper.selectByPrimaryKey(tenantId);
        storeDTO.setAuditor(tenant.getTenantName());

        //现金账号余额信息
        List<MerchantStoreBalance> merchantStoreBalance = merchantStoreBalanceMapper.selectList(new LambdaQueryWrapper<MerchantStoreBalance>().eq(MerchantStoreBalance::getStoreId, id).eq(MerchantStoreBalance::getTenantId, tenantId));
        BigDecimal balance = merchantStoreBalance.stream().filter(item -> Objects.equals(item.getAccountType(), MerchantStoreBalanceEnums.AccountTypeEnum.CASH.getType())).map(MerchantStoreBalance::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        storeDTO.setBalance(balance);

        //非现金账号余额
        BigDecimal fundAccountBalance = merchantStoreBalance.stream().filter(item -> Objects.equals(item.getAccountType(), MerchantStoreBalanceEnums.AccountTypeEnum.NON_CASH.getType())).map(MerchantStoreBalance::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        storeDTO.setNonCashBalance(fundAccountBalance);

        //地址信息
        MerchantAddressResultResp address = merchantAddressService.selectByStoreId(tenantId, id);
        if (Objects.nonNull(address)) {
            storeDTO.setProvince(address.getProvince());
            storeDTO.setArea(address.getArea());
            storeDTO.setCity(address.getCity());
            storeDTO.setAddress(address.getAddress());
            storeDTO.setHouseNumber(address.getHouseNumber());
            storeDTO.setPoiNote(address.getPoiNote());
        }

        //账号信息
        List<MerchantStoreAccountDTO> merchantStoreAccounts = merchantStoreAccountService.selectByStoreId(tenantId, id, MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
        storeDTO.setAccountList(merchantStoreAccounts.stream().sorted(Comparator.comparing(MerchantStoreAccountDTO::getType).thenComparing(MerchantStoreAccountDTO::getId)).collect(Collectors.toList()));

        //联系人信息
        List<MerchantContactResultResp> resultRespList = merchantContactService.selectByStoreId(tenantId, id);
        List<MerchantContactDTO> contactList = resultRespList.stream().map(resp -> {
            MerchantContactDTO merchantContactDTO = new MerchantContactDTO();
            merchantContactDTO.setContactName(resp.getName());
            merchantContactDTO.setPhone(resp.getPhone());
            merchantContactDTO.setDefaultFlag(resp.getDefaultFlag());
            merchantContactDTO.setContactId(resp.getId());
            return merchantContactDTO;
        }).collect(Collectors.toList());
        storeDTO.setContactList(contactList);

        // 门店分组
        MerchantStoreGroupResultResp merchantStoreGroupResultResp = merchantStoreGroupService.selectByStoreId(storeDTO.getId(), tenantId);
        if (!Objects.isNull(merchantStoreGroupResultResp)) {
            storeDTO.setGroupId(merchantStoreGroupResultResp.getMerchantStoreGroupId());
            MerchantStoreGroupPageResultResp merchantStoreGroup = merchantStoreGroupService.queryById(merchantStoreGroupResultResp.getMerchantStoreGroupId(), tenantId);
            if (Objects.nonNull(merchantStoreGroup)) {
                storeDTO.setGroupName(merchantStoreGroup.getMerchantStoreGroupName());
            }
        }

        TenantStoreConfigVO tenantStoreConfigVO = tenantStoreConfigService.selectTenantStoreConfig(id, TenantStoreConfigEnum.StoreConfig.DELIVERY_NOTE_PRINT_PRICE.getConfigKey());
        storeDTO.setEnableDeliveryNotePrintPrice(NumberUtils.toInt(tenantStoreConfigVO.getConfigValue(), 1));

        return storeDTO;
    }

//    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultDTO updateStore(MerchantStoreDTO storeDTO, LoginContextInfoDTO contextInfoDTO) {
        // 门店基本信息校验
        ResultDTO checkResultDTO = checkStoreInfo(storeDTO);
        if (!checkResultDTO.isSuccess()) {
            return checkResultDTO;
        }
        if (StringUtils.isBlank(storeDTO.getStoreNo())) {
            return ResultDTO.fail(ResultDTOEnum.UPDATE_STORE_NO_CHECK_ERROR.getCode(), ResultDTOEnum.UPDATE_STORE_NO_CHECK_ERROR.getMessage());
        }

        // 判断品牌方是否已设置账期规则，未设置需要设置
        if (storeDTO.getBillSwitch().equals(BillSwitchEnum.OPEN.getCode())) {
            FinancialBillRuleDTO financialBillRuleDTO = financialBillRuleService.queryTenantFinancialRule(contextInfoDTO.getTenantId());
            if (financialBillRuleDTO == null) {
                throw new CallerException(ResultDTOEnum.NOT_SET_BILL_RULE.getCode(), ResultDTOEnum.NOT_SET_BILL_RULE.getMessage());
            }
        }

        Long storeId = storeDTO.getId();
//        MerchantStore store = merchantStoreMapper.selectByPrimaryKey(storeId);
        MerchantStore store = MerchantStoreMapperConvert.INSTANCE.respToMerchantStore(userCenterMerchantStoreFacade.getMerchantStoreById(storeId));
        if (Objects.isNull(store)) {
            return ResultDTO.fail(ResultDTOEnum.STORE_NOT_FOUND);
        }
        Long tenantId = store.getTenantId();
//        // 门店名去重
//        if (!Objects.equals(store.getStoreName(), storeDTO.getStoreName())) {
//            MerchantStore merchantStore = merchantStoreMapper.selectByStoreName(contextInfoDTO.getTenantId(), storeDTO.getStoreName());
//            if (Objects.nonNull(merchantStore)) {
//                return ResultDTO.fail(ResultDTOEnum.STORE_NAME_REPEAT);
//            }
//        }

        // 获取租户信息
        TenantDTO tenantBaseInfo = tenantService.getTenantBaseInfo(tenantId);
        if (Objects.isNull(tenantBaseInfo)) {
            return ResultDTO.fail(ResultDTOEnum.TENANT_NOT_FOUND);
        }


        // 判定是否有账期、余额权限
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        Pair<Boolean, Boolean> pair = authMenuPurviewService.checkAuthByCache(AuthQueryDTO.builder().tenantId(tenantId).authUserId(authUserId).build(), AuthTypeEnum.MANAGER_BILL.getUrl(), AuthTypeEnum.MANAGER_BALANCE_SWITCH.getUrl());
        log.info("账期、余额权限判定,tenantId:{},authUserId:{},账期权限:{},专享余额权限:{}", contextInfoDTO.getTenantId(), contextInfoDTO.getAuthUserId(),
                pair.getLeft(), pair.getRight());
        storeDTO.setBillSwitch(pair.getLeft() ? storeDTO.getBillSwitch() : null);
        storeDTO.setBalanceAuthority(pair.getRight() ? storeDTO.getBalanceAuthority() : null);

        // 保存更新日志
        saveUpdateLog(storeDTO,MerchantStoreMapperConvert.INSTANCE.convert2StoreDto(store));

        MerchantStoreDomainCommandReq merchantStoreDomainCommandReq = new MerchantStoreDomainCommandReq();
        MerchantStoreCommandReq merchantStoreCommandReq = MerchantStoreMapperConvert.INSTANCE.dtoToStoreCommandReq(storeDTO);
        Integer auditFlag = storeDTO.getAuditFlag();
        if (Objects.nonNull(auditFlag)) {
            merchantStoreCommandReq.setStatus(auditFlag);
            merchantStoreCommandReq.setAuditTime(LocalDateTime.now());
            merchantStoreCommandReq.setAuditRemark(storeDTO.getAuditRemark());
        }
        merchantStoreCommandReq.setTenantId(tenantId);
        merchantStoreDomainCommandReq.setMerchantStore(merchantStoreCommandReq);
        merchantStoreDomainCommandReq.setStoreId(storeId);
        merchantStoreDomainCommandReq.setGroupId(storeDTO.getGroupId());
        //更新地址信息
        MerchantAddressResultResp address = merchantAddressService.selectByStoreId(tenantId, storeId);
        MerchantAddressCommandReq updateAddress = new MerchantAddressCommandReq();
        updateAddress.setTenantId(tenantId);
        updateAddress.setId(address.getId());
        updateAddress.setStoreId(storeDTO.getId());
        updateAddress.setProvince(storeDTO.getProvince());
        updateAddress.setCity(storeDTO.getCity());
        updateAddress.setArea(storeDTO.getArea());
        updateAddress.setAddress(storeDTO.getAddress());
        updateAddress.setHouseNumber(storeDTO.getHouseNumber());
        updateAddress.setPoiNote(storeDTO.getPoiNote());
        updateAddress.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        //更新联系人信息
        List<MerchantContactCommandReq> merchantContactList = Lists.newArrayList();
        List<MerchantContactDTO> contactList = storeDTO.getContactList();
        for (MerchantContactDTO contact : contactList) {
            MerchantContactCommandReq mc = new MerchantContactCommandReq();
            mc.setId(contact.getContactId());
            mc.setTenantId(tenantId);
            mc.setAddressId(address.getId());
            mc.setName(contact.getContactName());
            mc.setPhone(contact.getPhone());
            mc.setDefaultFlag(contact.getDefaultFlag());
            merchantContactList.add(mc);
        }
        updateAddress.setMerchantContactList(merchantContactList);
        merchantStoreDomainCommandReq.setMerchantAddressList(Collections.singletonList(updateAddress));

        //更新账号信息
        List<MerchantStoreAccountCommandReq> merchantStoreAccountList = Lists.newArrayList();
        for (MerchantStoreAccountDTO accountDTO : storeDTO.getAccountList()) {
            MerchantStoreAccountCommandReq account = new MerchantStoreAccountCommandReq();
            account.setId(accountDTO.getId());
            account.setTenantId(tenantId);
            account.setStoreId(storeId);
            account.setAccountName(accountDTO.getAccountName());
            account.setPhone(accountDTO.getPhone());
            account.setType(accountDTO.getType());
            account.setUsername(accountDTO.getUsername());
            if (Objects.nonNull(auditFlag)) {
                account.setAuditTime(LocalDateTime.now());
                account.setStatus(auditFlag);
            }
            merchantStoreAccountList.add(account);
        }
        merchantStoreDomainCommandReq.setMerchantStoreAccountList(merchantStoreAccountList);
        Boolean update = userCenterMerchantStoreFacade.updateMerchantStoreInfo(merchantStoreDomainCommandReq);
        if (!Boolean.TRUE.equals(update)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }

        // 修改门店 配送单展示价格
        saveUpdateEnableDeliveryNotePrintPrice(tenantId, storeId, storeDTO.getEnableDeliveryNotePrintPrice());

        // 发送霸王茶姬的门店信息变更
        if (AuditFlagEnum.AUDIT_SUCCESS.getFlag().equals(auditFlag)
                || MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode().equals(storeDTO.getStatus())){
            log.info("发送霸王茶姬的门店信息变更 storeId >>> {}", storeId);
            merchantDeliveryInfoService.sendMerchantDeliveryInfo(storeId);
        }

        // 发送密码
        sendMerchantPassword(storeDTO, storeId, tenantId, tenantBaseInfo.getTenantName());

        if (!AuditFlagEnum.AUDIT_SUCCESS.getFlag().equals(auditFlag)) {
            log.info("updateStore 非审核成功，快速返回 storeDTO:{}", JSON.toJSONString(storeDTO));
            return ResultDTO.success();
        }
        initMerchantStoreBalanceIfNeed(merchantStoreCommandReq.getStatus(), storeDTO.getStoreNo(), storeId, tenantId);
        return ResultDTO.success();
    }

    private void sendMerchantPassword(MerchantStoreDTO storeDTO, Long storeId, Long tenantId, String tenantName){
        Integer auditFlag = storeDTO.getAuditFlag();
        List<MerchantStoreAccountDTO> accountList = new ArrayList<>();
        // 审核操作
        if (Objects.nonNull(auditFlag)) {
            // 对所有的账户都发送
            accountList = storeDTO.getAccountList();
        } else {
            // 新增门店：发送全部的账户
            // 编辑操作：只发送新增的账户
            accountList = storeDTO.getAccountList().stream().filter(dto -> Objects.isNull(dto.getId())).collect(Collectors.toList());
        }
        log.info("开始处理账户密码。待处理列表：accountList:{}", JSON.toJSONString(accountList));
        accountList.forEach(account -> this.sendMerchantPasswordInternal(account, storeId, tenantId, tenantName));
    }


    private void sendMerchantPasswordInternal(MerchantStoreAccountDTO merchantStoreAccountDTO, Long storeId, Long tenantId, String tenantName){
        String phone = merchantStoreAccountDTO.getPhone();
        List<MerchantStoreAccount> storeAccounts = merchantStoreAccountService.queryMerchantStoreAccount(phone, tenantId);
        // 当前账户
        MerchantStoreAccount currentAccount =  storeAccounts.stream().filter(it -> it.getStoreId().equals(storeId)).collect(Collectors.toList()).get(0);

        // 其他门店的账户
        storeAccounts =  Optional.ofNullable(storeAccounts).orElse(Collections.emptyList()).stream().filter(it -> it.getTenantId().equals(tenantId)).filter(it -> !it.getStoreId().equals(storeId)).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(storeAccounts)) {
            log.info("账户id:{}, phone:{}沿用之前其他门店的密码", currentAccount.getId(), phone);
            // 先判断当前租户下是否已经存在门店了，存在则沿用之前的密码
            AuthUserPasswordUpdateInput updateDTO = AuthUserPasswordUpdateInput.builder().bizUserId(currentAccount.getId()).tenantId(currentAccount.getTenantId()).origin(SystemOriginEnum.COSFO_MALL.getType()).keepOldPassword(true).build();
            authUserFacade.updateAuthUserPassword(updateDTO);
        } else {
            log.info("账户id:{}, phone:{}使用新创建密码", currentAccount.getId(), phone);
            // 初始化密码
            String password = PasswordUtil.generatePassword(currentAccount);
            // auth修改初始密码
            AuthUserPasswordUpdateInput updateDTO = AuthUserPasswordUpdateInput.builder().bizUserId(currentAccount.getId()).tenantId(currentAccount.getTenantId()).origin(SystemOriginEnum.COSFO_MALL.getType()).password(password).build();
            authUserFacade.updateAuthUserPassword(updateDTO);
            // 发送通知
            // 这里只有用户名不为手机号的时候 才需要发送通知：需求by佳一
            if(Objects.equals(currentAccount.getUsername(), phone)) {
                log.info("账户id:{}, phone:{}用户名与手机号一致，不需要发送通知", currentAccount.getId(), phone);
                return;
            }

            Long sceneId = SmsConstants.MERCHANT_ACCOUNT_OPEN;
            List<String> args = Arrays.asList(phone, tenantName, currentAccount.getUsername(), password);
            Sms sms = new Sms();
            sms.setPhone(phone);
            sms.setSceneId(sceneId);
            sms.setArgs(args);
            noticeService.sendSmsCode(sms);
        }
    }



    private void saveUpdateEnableDeliveryNotePrintPrice(Long tenantId, Long storeId, Integer enableDeliveryNotePrintPrice){
        TenantStoreConfigVO tenantStoreConfigVO = new TenantStoreConfigVO();
        tenantStoreConfigVO.setTenantId(tenantId);
        tenantStoreConfigVO.setStoreId(storeId);
        tenantStoreConfigVO.setConfigKey(TenantStoreConfigEnum.StoreConfig.DELIVERY_NOTE_PRINT_PRICE.getConfigKey());
        tenantStoreConfigVO.setConfigValue(String.valueOf(enableDeliveryNotePrintPrice));

        tenantStoreConfigService.saveOrUpdateTenantStoreConfig(tenantStoreConfigVO);
    }

    private void saveUpdateLog(MerchantStoreDTO newStoreInfo,MerchantStoreDTO oldStore) {
        try {
            Long id = newStoreInfo.getId();
            BizLogRecordContext.put("storeId", id);
            if (Objects.isNull(oldStore)) {
                log.error("更新日志时，未找到原门店信息！门店ID:{}", id);
                return;
            }
            Long tenantId = oldStore.getTenantId();
            BizLogRecordContext.put("tenantId", tenantId);
            //地址信息
            MerchantAddressResultResp address = merchantAddressService.selectByStoreId(tenantId, id);
            if (Objects.nonNull(address)) {
                oldStore.setProvince(address.getProvince());
                oldStore.setArea(address.getArea());
                oldStore.setCity(address.getCity());
                oldStore.setAddress(address.getAddress());
                oldStore.setHouseNumber(address.getHouseNumber());
                oldStore.setPoiNote(address.getPoiNote());
            }

            // 门店分组
            MerchantStoreGroupResultResp merchantStoreGroupResultResp = merchantStoreGroupService.selectByStoreId(oldStore.getId(), tenantId);
            if (!Objects.isNull(merchantStoreGroupResultResp)) {
                oldStore.setGroupId(merchantStoreGroupResultResp.getMerchantStoreGroupId());
                oldStore.setGroupName(merchantStoreGroupResultResp.getMerchantStoreGroupName());
            }
            if (!oldStore.getGroupId().equals(newStoreInfo.getGroupId())){
                MerchantStoreGroupPageResultResp newGroup = merchantStoreGroupService.queryById(newStoreInfo.getGroupId(), tenantId);
                newStoreInfo.setGroupName(Optional.ofNullable(newGroup).map(MerchantStoreGroupPageResultResp::getMerchantStoreGroupName).orElse(""));
            }

            //账号信息
            List<MerchantStoreAccountDTO> merchantStoreAccounts = merchantStoreAccountService.selectByStoreId(tenantId, id, MerchantStoreAccountDeleteFlagEnum.NORMAL.getStatus());
            oldStore.setAccountList(merchantStoreAccounts.stream().sorted(Comparator.comparing(MerchantStoreAccountDTO::getType).thenComparing(MerchantStoreAccountDTO::getId)).collect(Collectors.toList()));

            //联系人信息
            List<MerchantContactResultResp> resultRespList = merchantContactService.selectByStoreId(tenantId, id);
            List<MerchantContactDTO> contactList = resultRespList.stream().map(resp -> {
                MerchantContactDTO merchantContactDTO = new MerchantContactDTO();
                merchantContactDTO.setContactName(resp.getName());
                merchantContactDTO.setPhone(resp.getPhone());
                merchantContactDTO.setDefaultFlag(resp.getDefaultFlag());
                merchantContactDTO.setContactId(resp.getId());
                return merchantContactDTO;
            }).collect(Collectors.toList());
            oldStore.setContactList(contactList);

            Map<String, Object> content = new HashMap<>();
            content.put("oldStore", oldStore);
            content.put("newStore", newStoreInfo);
            BizLogRecordContext.put("content", content);
        } catch (Exception e) {
            log.error("获取日志上下文失败！", e);
        }
    }
    void batchInitMerchantStoreBalance(List<Long> storeIds){
        if(CollectionUtil.isEmpty (storeIds)){
            return;
        }
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList (storeIds);
        if (CollectionUtil.isNotEmpty (merchantStoreList)) {
            // 初始化门店余额信息
            List<MerchantStoreBalance> result = merchantStoreList.stream ().map (merchantStore -> {
                MerchantStoreBalance merchantStoreBalance = new MerchantStoreBalance ();
                merchantStoreBalance.setStoreId (merchantStore.getId ());
                merchantStoreBalance.setStoreNo (merchantStore.getStoreNo ());
                merchantStoreBalance.setTenantId (merchantStore.getTenantId ());
                merchantStoreBalance.setBalance (BigDecimal.ZERO);
                merchantStoreBalance.setAccountType(MerchantStoreBalanceEnums.AccountTypeEnum.CASH.getType());
                return merchantStoreBalance;
            }).collect (Collectors.toList ());
            merchantStoreBalanceMapper.batchInsert (result);
        }
    }
    @Override
    public void initMerchantStoreBalanceIfNeed(Integer storeStatus, String storeNo, Long storeId, Long tenantId) {
        if (!MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode().equals(storeStatus)) {
            return;
        }
        // 审核成功,决定是否需要初始化余额
        MerchantStoreBalance existMerchantStoreBalance = merchantStoreBalanceMapper.selectByStoreId(tenantId, storeId, MerchantStoreBalanceEnums.AccountTypeEnum.CASH.getType(), null);
        if (Objects.nonNull(existMerchantStoreBalance)) {
            return;
        }

        MerchantStoreBalance merchantStoreBalance = new MerchantStoreBalance();
        merchantStoreBalance.setStoreId(storeId);
        merchantStoreBalance.setTenantId(tenantId);
        merchantStoreBalance.setStoreNo(storeNo);
        merchantStoreBalance.setBalance(BigDecimal.ZERO);
        merchantStoreBalance.setAccountType(MerchantStoreBalanceEnums.AccountTypeEnum.CASH.getType());
        merchantStoreBalanceMapper.insert(merchantStoreBalance);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ExcelImportResDTO saveStoreCodeMapping(MultipartFile file, Long tenantId) {
        List<StoreCodeMappingDTO> mappingDTOS;
        List<StoreCodeMappingDTO> errorStoreList = new ArrayList<>();
        ExcelImportResDTO resDTO = new ExcelImportResDTO();
        try {
            mappingDTOS = ExcelUtils.read(file.getInputStream(), StoreCodeMappingDTO.class);
        } catch (Exception e) {
            log.warn("POS STORE excel 导入错误", e);
            throw new BizException ("请检查模版是否正确");
        }
        if (CollectionUtils.isEmpty(mappingDTOS)) {
            log.info("门店映射导入数据为空, tenantId={}", tenantId);
            throw new BizException("导入数据为空");
        }

        if (mappingDTOS.size() > 500) {
            throw new BizException("单次导入数据不能超过500条");
        }
        log.info("门店映射导入数据, tenantId={}, total={}", tenantId, mappingDTOS.size());

        MerchantStoreQueryReq queryReq = new MerchantStoreQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setStoreNoList(mappingDTOS.stream().map(StoreCodeMappingDTO::getTargetCode).collect(Collectors.toList()));
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(queryReq);

        // channelType 只能是一种
        if (mappingDTOS.stream().map(StoreCodeMappingDTO::getChannelTypeDesc).distinct().count() > 1) {
            throw new BizException("单次导入仅支持一个渠道");
        }

        DocCodeChannelTypeEnum channelTypeEnum = DocCodeChannelTypeEnum.getByDesc(mappingDTOS.get(0).getChannelTypeDesc());
        if (channelTypeEnum == null) {
            throw new BizException("渠道类型不存在");
        }
        // 查询已经存在的映射关系
        List<DocCodeMappingDTO> docCodeMappingDTOS = docCodeMappingService.selectByOutCodeAndType(mappingDTOS.stream().map(StoreCodeMappingDTO::getOutCode).collect(Collectors.toSet()), DocCodeTargetTypeEnum.MARKET_ITEM.getCode(), channelTypeEnum.getCode(), tenantId);
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(docCodeMappingDTOS)) {
            docCodeMappingService.deleteByIds(docCodeMappingDTOS.stream().map(DocCodeMappingDTO::getId).collect(Collectors.toList()));
        }

        log.info("门店映射导入数据, tenantId={}, 门店总数={}", tenantId, merchantStoreList.size());
        List<DocCodeMappingDTO> collect = mappingDTOS.stream().map(mapping -> {
            if (!mapping.validateFields()) {
                errorStoreList.add(mapping);
                return null;
            }
            DocCodeChannelTypeEnum channelType = DocCodeChannelTypeEnum.getByDesc(mapping.getChannelTypeDesc());
            Optional<MerchantStoreResultResp> first = merchantStoreList.stream().filter(el -> Objects.equals(el.getStoreNo(), mapping.getTargetCode())).findFirst();
            if (!first.isPresent()) {
                mapping.setErrorMsg("门店不存在");
                errorStoreList.add(mapping);
                return null;
            }
            DocCodeMappingDTO docCodeMappingDTO = DocCodeMappingConverter.INSTANCE.toDocCodeMappingDTO(mapping);
            docCodeMappingDTO.setTenantId(tenantId);
            docCodeMappingDTO.setTargetType(DocCodeTargetTypeEnum.STORE.getCode());
            docCodeMappingDTO.setChannelType(channelType.getCode());
            return docCodeMappingDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            docCodeMappingService.saveCodeMapping(collect);
        }
        resDTO.setSuccessRow(collect.size());
        resDTO.setFailRow(errorStoreList.size());
        // 异常数据写入excel
        if (errorStoreList.isEmpty()) {
            return resDTO;
        }

        String filePath = commonService.exportExcel(errorStoreList, ExcelTypeEnum.ERROR_POS_STORE.getName());
        String qiNiuFilePath = null;
        try {
            qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "三方POS门店导入错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
        } catch (IOException e) {
            throw new BizException(e);
        }
        resDTO.setErrorFilePath(qiNiuFilePath);
        //删除临时文件
        commonService.deleteFile(filePath);
        return resDTO;
    }

    @Override
    public String exportThirdMapTemplate() {
        SystemParameters parameters = systemParametersService.selectByKey(Constants.STORE_DOC_MAPPING_IMPORT_TEMPLATE_PATH);
        return parameters.getParamValue();
    }

    /**
     * 校验门店信息
     *
     * @param storeDTO
     * @return
     */
    private ResultDTO checkStoreInfo(MerchantStoreDTO storeDTO) {
        if (StringUtils.isBlank(storeDTO.getProvince()) || StringUtils.isBlank(storeDTO.getCity()) || StringUtils.isBlank(storeDTO.getArea())) {
            return ResultDTO.fail(ResultDTOEnum.STORE_ADDRESS_ERROR.getCode(), ResultDTOEnum.STORE_ADDRESS_ERROR.getMessage());
        }
        //城市信息校验
        List<CommonLocationCityDTO> commonLocationCityDTOS = locationCityService.queryByCityNames (Collections.singleton (storeDTO.getCity ()));
        if (CollectionUtils.isEmpty (commonLocationCityDTOS)) {
            return ResultDTO.fail(ResultDTOEnum.STORE_ADDRESS_ERROR.getCode(), ResultDTOEnum.STORE_ADDRESS_ERROR.getMessage());
        }
        if (StringUtils.isBlank(storeDTO.getStoreName()) || !StringUtils.isStoreName(storeDTO.getStoreName())) {
            return ResultDTO.fail(ResultDTOEnum.STORE_NAME_ERROR.getCode(), ResultDTOEnum.STORE_NAME_ERROR.getMessage());
        }
        if (!StringUtils.isAddress(storeDTO.getAddress())) {
            return ResultDTO.fail(ResultDTOEnum.STORE_ADDRESS_ERROR.getCode(), ResultDTOEnum.STORE_ADDRESS_ERROR.getMessage());
        }
        if (!StringUtils.isBlank(storeDTO.getHouseNumber()) && !StringUtils.isHouseNumber(storeDTO.getHouseNumber())) {
            return ResultDTO.fail(ResultDTOEnum.STORE_HOUSE_NUMBER_ERROR.getCode(), ResultDTOEnum.STORE_HOUSE_NUMBER_ERROR.getMessage());
        }
        if (Objects.isNull(BalanceAuthorityTypeEnum.getByType(storeDTO.getBalanceAuthority()))) {
            return ResultDTO.fail(ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.getMessage());
        }
        // 账号信息校验
        List<MerchantStoreAccountDTO> accountList = storeDTO.getAccountList();
        if (CollectionUtils.isEmpty(accountList)) {
            return ResultDTO.fail("账号信息必填");
        }
        long managerNum = accountList.stream().filter(el -> Objects.equals(el.getType(), MerchantAccountTypeEnum.MANAGER.getType())).count();
        if (managerNum > NumberConstant.ONE) {
            return ResultDTO.fail("店长只能有一个");
        }
        if (accountList.size() > NumberConstants.ELEVEN) {
            return ResultDTO.fail("最多有一个店长和十个店员");
        }
        long count = accountList.stream().map(MerchantStoreAccountDTO::getPhone).distinct().count();
        if (count < accountList.size()) {
            return ResultDTO.fail("同一个门店下不能有相同的手机号码");
        }

        return ResultDTO.success();
    }

    @Override
    public List<MerchantStoreResultResp> queryMerchantStore(Integer storeType, String storeName, String phone, Long tenantId) {
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setType(storeType);
        merchantStoreQueryReq.setStoreName(storeName);
        merchantStoreQueryReq.setPhone(phone);
        merchantStoreQueryReq.setTenantId(tenantId);
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
//        List<MerchantStore> merchantStores = merchantStoreMapper.queryMerchantStore(storeType, storeName, phone, tenantId);
        return merchantStoreList;
    }

    @Override
    public MerchantStoreDTO queryStore(Long storeId, Long tenantId) {
//        MerchantStore merchantStore = merchantStoreMapper.selectByPrimaryKey(storeId);
        MerchantStoreResultResp merchantStore = userCenterMerchantStoreFacade.getMerchantStoreById(storeId);
        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
        BeanUtils.copyProperties(merchantStore, merchantStoreDTO);
        return merchantStoreDTO;
    }

    @Override
    public Integer waitAuditStoreNum(LoginContextInfoDTO loginContextInfoDTO) {
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setTenantId(loginContextInfoDTO.getTenantId());
        merchantStoreQueryReq.setStatus(MerchantStoreEnum.Status.IN_AUDIT.getCode());
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
        return Optional.ofNullable(merchantStoreList).map(List::size).orElse(NumberConstant.ZERO);
    }

    @Override
    public Integer placeOrderDeadlineNum(LoginContextInfoDTO loginContextInfoDTO) {
        MerchantStorePageQueryReq queryReq = new MerchantStorePageQueryReq ();
        TenantNumberCommonConfigVO tenantNumberCommonConfigVO = tenantCommonConfigService.queryTenantConfig (loginContextInfoDTO.getTenantId (), TenantConfigEnum.TenantConfig.PLACE_ORDER_PERMISSION_EXPIRY_TIME.getConfigKey ());
        queryReq.setPlaceOrderPermissionExpiryTime (tenantNumberCommonConfigVO.getConfigValue ());
        queryReq.setTenantId (loginContextInfoDTO.getTenantId ());
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(1);
        pageQueryReq.setPageSize(1);
        PageInfo<MerchantStorePageResultResp> merchantStorePage = userCenterMerchantStoreFacade.getMerchantStorePage (queryReq, pageQueryReq);
        return ObjectUtil.isNotNull (merchantStorePage.getTotal ())?Long.valueOf (merchantStorePage.getTotal ()).intValue ():0;
    }

    @Override
    public ExcelImportResDTO batchStoreRegister(MultipartFile file, LoginContextInfoDTO loginContextInfoDTO, HttpServletResponse response) throws IOException {
        String cacheKey = RedisKeyEnum.CM00013.join(loginContextInfoDTO.getTenantId ());
        if (Objects.nonNull(redisUtils.get(cacheKey))) {
            throw new DefaultServiceException ("导入门店频率过高 请稍后重试~");
        }

        List<MerchantStoreExcelDataDTO> storeList = null;
        try{
            storeList = ExcelUtils.read(file.getInputStream(), MerchantStoreExcelDataDTO.class);
        }catch (Exception e){
            log.error ("batchStoreRegister,读取失败，tenantid={}",loginContextInfoDTO.getTenantId (),e);
            throw new DefaultServiceException ("表格读取失败，请检查数据格式后重新试试，或联系客服处理一下~");
        }
        if (CollectionUtils.isEmpty(storeList)) {
            throw new DefaultServiceException (ResultDTOEnum.EXCEL_STORE_INFO_EMPTY.getMessage ());
        }

        // 限制导入门店记录数250
        if (storeList.size() > 250) {
            throw new DefaultServiceException (ResultDTOEnum.EXCEL_STORE_OVER_AMOUNT.getMessage ());
        }

        Integer quantity = marketItemFacade.countOnsaleItem(loginContextInfoDTO.getTenantId ());
        if(quantity * storeList.size() > 15000){
            throw new DefaultServiceException ("导入门店数量超载 请分批导入~");
        }
        // 提取相同元素
        Set<String> repeatNames = storeList.stream().collect(Collectors.groupingBy(MerchantStoreExcelDataDTO::getStoreName)).values()
                .stream().filter(v->v.size()>1).reduce(new ArrayList<>(), (a,b)->{a.addAll(b);return a;}).stream().map (MerchantStoreExcelDataDTO::getStoreName).collect(Collectors.toSet ());

        Set<String> repeatNos = storeList.stream().filter (e->!StringUtils.isBlank (e.getStoreNo ())).collect(Collectors.groupingBy(MerchantStoreExcelDataDTO::getStoreNo)).values()
                .stream().filter(v->v.size()>1).reduce(new ArrayList<>(), (a,b)->{a.addAll(b);return a;}).stream().map (MerchantStoreExcelDataDTO::getStoreNo).collect(Collectors.toSet ());

        List<String> citys = locationCityService.queryAllCity ();
        // 判定是否有账期、余额权限
        Long tenantId = UserLoginContextUtils.getTenantId();
        RegionalOrganizationResultResp regionalOrganizationResultResp = userCenterMerchantStoreFacade.saasGetRegionalByTenantId(tenantId);
        if (Objects.isNull(regionalOrganizationResultResp)) {
            throw new BizException("租户默认区域组织不存在");
        }
        List<MerchantStoreDomainCommandReq> reqs = new ArrayList<> ();
        List<Long> merchantStoreIds = null;
        // 循环遍历
        List<MerchantStoreExcelDataDTO> errorStoreList = new ArrayList<>();
        for (MerchantStoreExcelDataDTO registerInfoDTO : storeList) {
            // 校验excel数据 有异常记录下来 写入excel上传
            if (!checkExcelStoreInfo(registerInfoDTO)) {
                errorStoreList.add(registerInfoDTO);
                continue;
            }
            //增加城市名称是否正确
            if (!citys.contains (registerInfoDTO.getCity ())) {
                registerInfoDTO.setErrorMessage ("城市错误");
                errorStoreList.add(registerInfoDTO);
                continue;
            }
            if (repeatNames.contains (registerInfoDTO.getStoreName ())) {
                registerInfoDTO.setErrorMessage ("excel内店铺名称重复");
                errorStoreList.add(registerInfoDTO);
                continue;
            }
            if (repeatNos.contains (registerInfoDTO.getStoreNo ())) {
                registerInfoDTO.setErrorMessage ("excel内店铺编码重复");
                errorStoreList.add(registerInfoDTO);
                continue;
            }
            MerchantStoreDTO storeDTO = transferToStoreDTO(registerInfoDTO,tenantId);
            MerchantStoreDomainCommandReq req = buildMerchantStoreDomainCommandReq (regionalOrganizationResultResp.getId (), storeDTO);
            reqs.add (req);
        }
        if(CollectionUtil.isNotEmpty (reqs)){
            List<MerchantStoreBatchImportResp> merchantStoreInfoBatch = userCenterMerchantStoreFacade.createMerchantStoreInfoBatch (reqs);
            if(CollectionUtil.isNotEmpty (merchantStoreInfoBatch)){
                Map<String, String> errorMap = merchantStoreInfoBatch.stream().filter (e->Objects.isNull (e.getStoreId ())).collect(Collectors.toList()).stream ().collect(Collectors.toMap(MerchantStoreBatchImportResp::getStoreName,MerchantStoreBatchImportResp::getErrorDesc));
                storeList.stream().forEach (e-> {
                    String reason = errorMap.get (e.getStoreName ());
                    if(Strings.isNotBlank (reason)){
                        e.setErrorMessage(reason);
                        errorStoreList.add (e);
                    }
                });
                merchantStoreIds = merchantStoreInfoBatch.stream().filter (e->!Objects.isNull (e.getStoreId ())).map (MerchantStoreBatchImportResp::getStoreId).collect(Collectors.toList());
            }
        }
        batchInitMerchantStoreBalance(merchantStoreIds);

        ExcelImportResDTO resDTO = new ExcelImportResDTO();
        resDTO.setFailRow(errorStoreList.size());
        resDTO.setSuccessRow(storeList.size() - resDTO.getFailRow());
        // 无异常数据
        if (CollectionUtils.isEmpty(errorStoreList)) {
            return resDTO;
        }

        // 异常数据写入excel
        String filePath = commonService.exportExcel(errorStoreList, ExcelTypeEnum.ERROR_MERCHANT_STORE.getName());
        String qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "门店错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xls");
        resDTO.setErrorFilePath(qiNiuFilePath);

        //删除临时文件
        commonService.deleteFile(filePath);
        redisUtils.set(cacheKey, NumberConstant.ONE, TimeUnit.MINUTES.toMillis(3));
        return resDTO;
    }

    private MerchantStoreDTO transferToStoreDTO(MerchantStoreExcelDataDTO registerInfoDTO,Long tenantId) {
        MerchantStoreDTO storeDTO = new MerchantStoreDTO();
        storeDTO.setTenantId(tenantId);
        storeDTO.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
        storeDTO.setStoreName(registerInfoDTO.getStoreName());
        storeDTO.setPhone(registerInfoDTO.getPhone());
        storeDTO.setProvince(registerInfoDTO.getProvince());
        storeDTO.setCity(!StringUtils.isEmpty(registerInfoDTO.getCity()) ? registerInfoDTO.getCity() : registerInfoDTO.getProvince());
        storeDTO.setArea(registerInfoDTO.getArea());
        storeDTO.setAddress(registerInfoDTO.getAddress());
        storeDTO.setHouseNumber(registerInfoDTO.getHouseNumber());
        storeDTO.setBillSwitch(Objects.equals(registerInfoDTO.getBillPermissions(), BillPermissionTypeEnum.CLOSE_BILL.getDesc()) ? BillSwitchEnum.SHUTDOWN.getCode() : BillSwitchEnum.OPEN.getCode());
        storeDTO.setBalanceAuthority(Objects.equals(registerInfoDTO.getBalancePermissions(), BalancePermissionTypeEnum.OPEN_BALANCE_AUTH.getDesc()) ? BalancePermissionTypeEnum.OPEN_BALANCE_AUTH.getType() : BalancePermissionTypeEnum.CLOSE_BALANCE_AUTH.getType());
        // 默认都保留在线支付能力
        storeDTO.setOnlinePayment(OnlinePaymentEnum.OPEN.getCode());
        storeDTO.setType(MerchantStoreEnum.Type.getStatus(registerInfoDTO.getType()).getCode());
        storeDTO.setRemark(registerInfoDTO.getRemark());
        storeDTO.setStoreNo(registerInfoDTO.getStoreNo());
        storeDTO.setEnableOfflinePayment (Objects.equals(registerInfoDTO.getOfflinePaymentPermissions(), EnableOfflinePaymentEnum.OPEN.getDesc()) ? EnableOfflinePaymentEnum.OPEN.getType () : EnableOfflinePaymentEnum.CLOSE.getType());
        List<MerchantContactDTO> contactList = new ArrayList<>();
        MerchantContactDTO contactDTO = new MerchantContactDTO();
        contactDTO.setContactName(registerInfoDTO.getContactName());
        contactDTO.setPhone(registerInfoDTO.getContactPhone());
        contactDTO.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        contactList.add(contactDTO);
        storeDTO.setContactList(contactList);

        MerchantStoreAccountDTO accountDto = new MerchantStoreAccountDTO();
        accountDto.setAccountName(registerInfoDTO.getStoreName());
        accountDto.setPhone(registerInfoDTO.getPhone());
        accountDto.setType(MerchantAccountTypeEnum.MANAGER.getType());
        storeDTO.setAccountList(Arrays.asList(accountDto));
        return storeDTO;
    }

    @Override
    public List<MerchantStoreDTO> batchQuery(List<Long> storeIds, Long tenantId) {
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(storeIds);
        if(CollectionUtil.isEmpty(merchantStoreList)){
            return Collections.EMPTY_LIST;
        }
        List<MerchantStoreResultResp> list = merchantStoreList.stream().filter(merchantStoreResultResp -> Objects.equals(merchantStoreResultResp.getTenantId(), tenantId)).collect(Collectors.toList());
        return MerchantStoreMapperConvert.INSTANCE.respListToMerchantStoreDtoList(list);
//        return merchantStoreMapper.batchQuery(tenantId, storeIds);
    }

    @Override
    public List<MerchantStoreDTO> batchQueryByStoreIds(List<Long> storeIds, Long tenantId) {
        return batchQuery(storeIds, tenantId);
    }

    @Override
    public List<MerchantStoreDTO> batchQueryDetailByStoreIds(List<Long> storeIds, Long tenantId) {
        List<MerchantStoreDTO> merchantStoreDTOS = batchQuery(storeIds, tenantId);
        if (CollectionUtil.isEmpty(merchantStoreDTOS)) {
            return merchantStoreDTOS;
        }
        List<MerchantDeliveryAddressResultDTO> merchantDeliveryAddressResultDTOS = merchantAddressService.selectByStoreIds(tenantId, storeIds);
        Map<Long, MerchantDeliveryAddressResultDTO> addressMap = merchantDeliveryAddressResultDTOS.stream().collect(Collectors.toMap(MerchantDeliveryAddressResultDTO::getStoreId, Function.identity(), (v1, v2) -> v1));

        List<MerchantStoreGroupResultResp> groupResultRespList = userCenterMerchantStoreGroupFacade.getGroupByStoreIds(tenantId, storeIds);
        Map<Long, MerchantStoreGroupResultResp> groupMap = Optional.ofNullable(groupResultRespList).map(list -> list.stream().collect(Collectors.toMap(MerchantStoreGroupResultResp::getStoreId, Function.identity(), (v1, v2) -> v1))).orElse(Collections.EMPTY_MAP);
        for (MerchantStoreDTO merchantStoreDTO : merchantStoreDTOS) {
            MerchantStoreGroupResultResp merchantStoreGroupResultResp = groupMap.get(merchantStoreDTO.getId());
            Optional.ofNullable(merchantStoreGroupResultResp).ifPresent(
                    resp -> {
                        merchantStoreDTO.setGroupId(resp.getMerchantStoreGroupId());
                        merchantStoreDTO.setGroupName(resp.getMerchantStoreGroupName());
                    }
            );
            MerchantDeliveryAddressResultDTO addressResultDTO = addressMap.get(merchantStoreDTO.getId());
            Optional.ofNullable(addressResultDTO).ifPresent(
                    resp -> {
                        merchantStoreDTO.setProvince(resp.getProvince());
                        merchantStoreDTO.setCity(resp.getCity());
                        merchantStoreDTO.setArea(resp.getArea());
                        merchantStoreDTO.setAddress(resp.getAddress());
                    }
            );
        }
        return merchantStoreDTOS;
    }
    public Long saveStoreInternal(MerchantStoreDTO merchantStoreDTO,Long regionalId) {
       Long merchantStoreId = userCenterMerchantStoreFacade.createMerchantStoreInfo(buildMerchantStoreDomainCommandReq (regionalId,merchantStoreDTO));
        // 保存门店记录日志
        merchantBizLogService.saveStoreLog(merchantStoreId,merchantStoreDTO.getStoreName());
        return merchantStoreId;
    }

    private MerchantStoreDomainCommandReq buildMerchantStoreDomainCommandReq(Long regionalId, MerchantStoreDTO merchantStoreDTO) {
        MerchantStoreDomainCommandReq merchantStoreDomainCommandReq = new MerchantStoreDomainCommandReq();
        MerchantStoreCommandReq merchantStoreCommandReq = MerchantStoreMapperConvert.INSTANCE.dtoToStoreCommandReq(merchantStoreDTO);
        merchantStoreCommandReq.setAuditTime(LocalDateTime.now());
        merchantStoreCommandReq.setRegionalId(regionalId);
        merchantStoreDomainCommandReq.setMerchantStore(merchantStoreCommandReq);
        merchantStoreDomainCommandReq.setGroupId(merchantStoreDTO.getGroupId());
        // 注册地址信息
        MerchantAddressCommandReq merchantAddress = new MerchantAddressCommandReq();
        merchantAddress.setTenantId(merchantStoreDTO.getTenantId());
        merchantAddress.setProvince(merchantStoreDTO.getProvince());
        merchantAddress.setCity(merchantStoreDTO.getCity());
        merchantAddress.setArea(merchantStoreDTO.getArea());
        merchantAddress.setAddress(merchantStoreDTO.getAddress());
        merchantAddress.setHouseNumber(merchantStoreDTO.getHouseNumber());
        merchantAddress.setPoiNote(merchantStoreDTO.getPoiNote());
        merchantAddress.setDefaultFlag(DefaultTypeEnum.DEFAULT.getCode());
        merchantAddress.setStatus(AuditFlagEnum.AUDIT_SUCCESS.getStatus());
        // 注册联系人信息
        List<MerchantContactCommandReq> merchantContactCommandReqList = Lists.newArrayList();
        List<MerchantContactDTO> contactList = merchantStoreDTO.getContactList();
        for (MerchantContactDTO contact : contactList) {
            MerchantContactCommandReq merchantContact = new MerchantContactCommandReq();
            merchantContact.setTenantId(merchantStoreDTO.getTenantId());
            merchantContact.setAddressId(merchantAddress.getId());
            merchantContact.setName(contact.getContactName());
            merchantContact.setPhone(contact.getPhone());
            merchantContact.setDefaultFlag(contact.getDefaultFlag());
            merchantContactCommandReqList.add(merchantContact);
        }
        merchantAddress.setMerchantContactList(merchantContactCommandReqList);
        merchantStoreDomainCommandReq.setMerchantAddressList(Collections.singletonList(merchantAddress));
        // 注册门店账户信息
        List<MerchantStoreAccountCommandReq> merchantStoreAccountList = Lists.newArrayList();
        List<MerchantStoreAccountDTO> accountList = merchantStoreDTO.getAccountList();
        for (MerchantStoreAccountDTO merchantStoreAccountDTO : accountList) {
            MerchantStoreAccountCommandReq merchantStoreAccount = new MerchantStoreAccountCommandReq();
            merchantStoreAccount.setTenantId(merchantStoreDTO.getTenantId());
            merchantStoreAccount.setAccountName(merchantStoreAccountDTO.getAccountName());
            merchantStoreAccount.setUsername(merchantStoreAccountDTO.getUsername());
            merchantStoreAccount.setPhone(merchantStoreAccountDTO.getPhone());
            merchantStoreAccount.setType(merchantStoreAccountDTO.getType());
            merchantStoreAccount.setRegisterTime(LocalDateTime.now());
            merchantStoreAccount.setAuditTime(LocalDateTime.now());
            merchantStoreAccount.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
            merchantStoreAccountList.add(merchantStoreAccount);
        }
        merchantStoreDomainCommandReq.setMerchantStoreAccountList(merchantStoreAccountList);
        return merchantStoreDomainCommandReq;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultDTO saveStore(MerchantStoreDTO merchantStoreDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 门店基本信息校验
        ResultDTO checkResultDTO = checkStoreInfo(merchantStoreDTO);
        if (!checkResultDTO.isSuccess()) {
            return checkResultDTO;
        }
        // 判定是否有账期、余额权限
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();

        // 获取租户信息
        TenantDTO tenantBaseInfo = tenantService.getTenantBaseInfo(tenantId);
        if (Objects.isNull(tenantBaseInfo)) {
            return ResultDTO.fail(ResultDTOEnum.TENANT_NOT_FOUND);
        }

        Pair<Boolean, Boolean> pair = authMenuPurviewService.checkAuthByCache(AuthQueryDTO.builder().tenantId(tenantId).authUserId(authUserId).build(), AuthTypeEnum.MANAGER_BILL.getUrl(), AuthTypeEnum.MANAGER_BALANCE_SWITCH.getUrl());
        log.info("账期、余额权限判定,tenantId:{},authUserId:{},账期权限:{},专享余额权限:{}", tenantId,authUserId, pair.getLeft(), pair.getRight());
        RegionalOrganizationResultResp regionalOrganizationResultResp = userCenterMerchantStoreFacade.saasGetRegionalByTenantId(tenantId);
        if (Objects.isNull(regionalOrganizationResultResp)) {
            throw new BizException("租户默认区域组织不存在");
        }

        fullMerchantStoreDTO(merchantStoreDTO,tenantId,pair);
        Long merchantStoreId = saveStoreInternal (merchantStoreDTO,regionalOrganizationResultResp.getId ());

        // 发送密码
        sendMerchantPassword(merchantStoreDTO, merchantStoreId, tenantId, tenantBaseInfo.getTenantName());

        // 修改门店 配送单展示价格
        saveUpdateEnableDeliveryNotePrintPrice(tenantId, merchantStoreId, merchantStoreDTO.getEnableDeliveryNotePrintPrice());

        batchInitMerchantStoreBalance(Lists.newArrayList (merchantStoreId));
        return ResultDTO.success();
    }

    private void fullMerchantStoreDTO(MerchantStoreDTO merchantStoreDTO, Long tenantId,Pair<Boolean, Boolean> pair) {
        merchantStoreDTO.setTenantId(tenantId);
        // 没有账期权限时，账期权限默认关闭状态
        merchantStoreDTO.setBillSwitch(pair.getLeft() ? merchantStoreDTO.getBillSwitch() : BillSwitchEnum.SHUTDOWN.getCode());
        merchantStoreDTO.setBalanceAuthority(pair.getRight() ? merchantStoreDTO.getBalanceAuthority() : null);
        merchantStoreDTO.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
    }

    @Override
    public ResultDTO updateOperateStatus(MerchantStoreDTO merchantStoreDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long id = merchantStoreDTO.getId();
//        MerchantStore merchantStore = merchantStoreMapper.selectByPrimaryKey(id);
        MerchantStore merchantStore = MerchantStoreMapperConvert.INSTANCE.respToMerchantStore(userCenterMerchantStoreFacade.getMerchantStoreById(id));
        Integer operateStatus = merchantStoreDTO.getOperateStatus();

        // 如果非经营中门店关店则给出提示
        if (Objects.equals(MerchantStoreEnum.OperateStatus.CLOSE.getCode(), operateStatus) && !Objects.equals(merchantStore.getStatus(), MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode())) {
            return ResultDTO.fail(ResultDTOEnum.ONLY_SUCCESS_COULD_CLOSE);
        }
        // 如果非关店中门店开店则给出提示
        if (Objects.equals(MerchantStoreEnum.OperateStatus.OPEN.getCode(), operateStatus) && !Objects.equals(merchantStore.getStatus(), MerchantStoreEnum.Status.CLOSE.getCode())) {
            return ResultDTO.fail(ResultDTOEnum.ONLY_CLOSE_COULD_OPEN);
        }

        // 更新门店状态
        Integer status = Objects.equals(operateStatus, MerchantStoreEnum.OperateStatus.OPEN.getCode()) ? MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode() : MerchantStoreEnum.Status.CLOSE.getCode();
//        MerchantStore update = new MerchantStore();
//        update.setId(id);
//        update.setStatus(status);
//        merchantStoreMapper.updateByPrimaryKeySelective(update);

        // 保存日志context
        saveUpdateOperateStatus(merchantStore, status);

        MerchantStoreCommandReq merchantStoreCommandReq = new MerchantStoreCommandReq();
        merchantStoreCommandReq.setId(id);
        merchantStoreCommandReq.setStatus(status);
        Boolean update = userCenterMerchantStoreFacade.updateMerchantStore(merchantStoreCommandReq);
        if (!Boolean.TRUE.equals(update)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }

        // 更新门店下账号状态
        List<MerchantStoreAccountDTO> merchantStoreAccountDtoList = merchantStoreAccountService.selectByStoreId(loginContextInfoDTO.getTenantId(), id, null);
        List<Long> accountIds = merchantStoreAccountDtoList.stream().map(MerchantStoreAccountDTO::getId).collect(Collectors.toList());
//        merchantStoreAccountMapper.updateStatusBatchByIds(accountIds, status);
        update = userCenterMerchantStoreAccountFacade.updateStatusBatch(accountIds, status);
        if (!Boolean.TRUE.equals(update)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }

        // TODO T掉门店
        merchantStoreAccountService.removeUserCache(accountIds);
        log.info("清除账号:{}缓存信息", id);

        return ResultDTO.success();
    }

    private void saveUpdateOperateStatus(MerchantStore oldStore, Integer status) {
        BizLogRecordContext.put("storeId", oldStore.getId());
        MerchantStoreDTO oldStoreDto = new MerchantStoreDTO();
        oldStoreDto.setId(oldStore.getId());
        oldStoreDto.setStatus(oldStore.getStatus());
        MerchantStoreDTO newStoreDto = new MerchantStoreDTO();
        newStoreDto.setId(oldStore.getId());
        newStoreDto.setStatus(status);

        Map<String, Object> content = new HashMap<>();
        content.put("oldStore", oldStoreDto);
        content.put("newStore", newStoreDto);
        BizLogRecordContext.put("content", content);
    }


    private Boolean checkExcelStoreInfo(MerchantStoreExcelDataDTO registerInfoDTO) {
        if (StringUtils.isBlank(registerInfoDTO.getStoreName())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.STORE_NAME_EMPTY.getMessage());
        } else if (StringUtils.isBlank(registerInfoDTO.getPhone())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.MANAGER_PHONE_EMPTY.getMessage());
        } else if (registerInfoDTO.getPhone().length() != 11) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.MANAGER_PHONE_ERROR.getMessage());
        } else if (StringUtils.isBlank(registerInfoDTO.getType())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.STORE_TYPE_EMPTY.getMessage());
        } else if (!Objects.equals(registerInfoDTO.getType(), MerchantStoreEnum.Type.DIRECT.getDesc()) && !Objects.equals(registerInfoDTO.getType(), MerchantStoreEnum.Type.JOINING.getDesc()) && !Objects.equals(registerInfoDTO.getType(), MerchantStoreEnum.Type.MANAGED.getDesc())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.STORE_TYPE_ERROR.getMessage());
        } else if (StringUtils.isBlank(registerInfoDTO.getBillPermissions())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.BILL_PERMISSION_EMPTY.getMessage());
        } else if (!Objects.equals(registerInfoDTO.getBillPermissions(), BillPermissionTypeEnum.OPEN_BILL_AND_ONLINE.getDesc()) && !Objects.equals(registerInfoDTO.getBillPermissions(), BillPermissionTypeEnum.CLOSE_BILL.getDesc())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.BILL_PERMISSION_ERROR.getMessage());
        }else if (Objects.isNull(BalancePermissionTypeEnum.getByDesc(registerInfoDTO.getBalancePermissions()))) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.BALANCE_PERMISSION_ERROR.getMessage());
        } else if (Objects.isNull(registerInfoDTO.getProvince())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.PROVINCE_EMPTY.getMessage());
        } else if (Objects.isNull(registerInfoDTO.getCity())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.CITY_EMPTY.getMessage());
        } else if (Objects.isNull(registerInfoDTO.getArea())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.AREA_EMPTY.getMessage());
        } else if (Objects.isNull(registerInfoDTO.getAddress())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.ADDRESS_EMPTY.getMessage());
        } else if (Objects.isNull(registerInfoDTO.getContactName())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.CONTACT_NAME_EMPTY.getMessage());
        } else if (Objects.isNull(registerInfoDTO.getContactPhone())) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.CONTACT_PHONE_EMPTY.getMessage());
        } else if (registerInfoDTO.getStoreName().length() > NumberConstants.TWENTY) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.STORE_NAME_TOO_LONG.getMessage());
        } else if (registerInfoDTO.getProvince().length() > NumberConstants.TWENTY || registerInfoDTO.getCity().length() > NumberConstants.TWENTY || registerInfoDTO.getArea().length() > NumberConstants.TWENTY) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.ADMINISTRATIVE_AREA_TOO_LONG.getMessage());
        } else if (registerInfoDTO.getContactPhone().length() != 11) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.CONTACT_PHONE_ERROR.getMessage());
        } else if (registerInfoDTO.getAddress().length() > NumberConstants.HUNDRED) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.ADDRESS_TOO_LONG.getMessage());
        } else if (!StringUtils.isBlank(registerInfoDTO.getRemark()) && registerInfoDTO.getRemark().length() > NumberConstants.TWO_HUNDRED_AND_FIFTY_FIVE) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.REMARK_TOO_LONG.getMessage());
        } else if (registerInfoDTO.getContactName().length() > NumberConstants.FIFTY) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.CONTACT_NAME_TOO_LONG.getMessage());
        } else if (!StringUtils.isBlank(registerInfoDTO.getHouseNumber()) && registerInfoDTO.getHouseNumber().length() > NumberConstants.HUNDRED) {
            registerInfoDTO.setErrorMessage(ResultDTOEnum.HOUSE_NUMBER_TOO_LONG.getMessage());
        } else if (!StringUtils.isEmpty(registerInfoDTO.getStoreNo())){
            if (registerInfoDTO.getStoreNo().length() > NumberConstants.TWENTY){
                registerInfoDTO.setErrorMessage(ResultDTOEnum.STORE_NO_OVER_LENGTH.getMessage());
            }
            String regex = Constants.REGEX_CHAR_AND_NUMBER;
            if (!registerInfoDTO.getStoreNo().matches(regex)){
                registerInfoDTO.setErrorMessage(ResultDTOEnum.STORE_NO_FORMAT_INCORRECT.getMessage());
            }
        }
        return Objects.isNull(registerInfoDTO.getErrorMessage()) ? Boolean.TRUE : Boolean.FALSE;
    }

    @Override
    public ResultDTO exportTemplate() {
        SystemParameters parameters = systemParametersService.selectByKey(Constants.STORE_IMPORT_TEMPLATE_PATH);
        return ResultDTO.success(parameters.getParamValue());
    }

    @Override
    public List<String> queryAllStoreCity(LoginContextInfoDTO loginContextInfoDTO) {
        List<String> list = merchantAddressService.queryAllStoreCity(loginContextInfoDTO.getTenantId());
        return list;
    }

    @Override
    public List<MerchantStoreDTO> listByCondition(MerchantStoreQueryDTO storeQueryDTO) {
        MerchantStorePageQueryReq queryReq = MerchantStoreMapperConvert.INSTANCE.queryDtoToQueryReq(storeQueryDTO);
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(NumberConstant.ONE);
        pageQueryReq.setPageSize(storeQueryDTO.getPageSize());
        PageInfo<MerchantStorePageResultResp> merchantStorePage = userCenterMerchantStoreFacade.getMerchantStorePage(queryReq, pageQueryReq);
        return MerchantStoreMapperConvert.INSTANCE.pageRespListToMerchantStoreDtoList(merchantStorePage.getList());

//        return merchantStoreMapper.listAll(storeQueryDTO);
    }

    @Override
    public List<MerchantStoreDTO> listByConditionNew(MerchantStoreQueryDTO storeQueryDTO) {
        return listAll(storeQueryDTO);
    }

    @Override
    public List<MerchantStoreVO> listMerchantStoreVO(List<Long> storeIds, Long tenantId) {
        List<MerchantStoreDTO> merchantStoreDtos = batchQueryByStoreIds(storeIds, tenantId);
        List<MerchantStoreGroupResultResp> merchantStoreGroupMappings = merchantStoreGroupService.selectByStoreIds(storeIds,tenantId);
        List<MerchantDeliveryAddressResultDTO> merchantAddressDTOS = merchantAddressService.selectByStoreIds(tenantId, storeIds);
        Map<Long, MerchantDeliveryAddressResultDTO> merchantAddressDtoMap = merchantAddressDTOS.stream().collect(Collectors.toMap(MerchantDeliveryAddressResultDTO::getStoreId, item -> item));
        Map<Long, List<MerchantStoreGroupResultResp>> mappingMap;
        Map<Long, MerchantStoreGroup> groupMap;
        if(!CollectionUtils.isEmpty(merchantStoreGroupMappings)){
            mappingMap =  merchantStoreGroupMappings.stream().collect(Collectors.groupingBy(MerchantStoreGroupResultResp::getStoreId));
//            List<MerchantStoreGroup> groups = merchantStoreGroupRepository.listByIds(merchantStoreGroupMappings.stream().map(MerchantStoreGroupMapping::getGroupId).collect(Collectors.toList()));
            List<Long> groupIds = merchantStoreGroupMappings.stream().map(MerchantStoreGroupResultResp::getMerchantStoreGroupId).collect(Collectors.toList());
            MerchantStoreGroupQueryDTO merchantStoreGroupQueryDTO = new MerchantStoreGroupQueryDTO();
            merchantStoreGroupQueryDTO.setGroupIds(groupIds);
            merchantStoreGroupQueryDTO.setTenantId(tenantId);
            List<MerchantStoreGroup> groups = merchantStoreGroupService.list(merchantStoreGroupQueryDTO);
            groupMap =  groups.stream().collect(Collectors.toMap(MerchantStoreGroup::getId, Function.identity()));
        } else {
            mappingMap = null;
            groupMap = null;
        }
        return merchantStoreDtos.stream().map(e->{
            MerchantStoreVO merchantStoreVO = new MerchantStoreVO();
            BeanUtils.copyProperties(e,merchantStoreVO);
            if(CollectionUtil.isNotEmpty(mappingMap)) {
                List<MerchantStoreGroupResultResp> storeGroupMapppngs = mappingMap.get(e.getId());
                MerchantStoreGroupResultResp merchantStoreGroupMapping = CollectionUtil.isNotEmpty(storeGroupMapppngs) ? storeGroupMapppngs.get(0):null;
                MerchantStoreGroup group = ObjectUtil.isNotNull(merchantStoreGroupMapping)? groupMap.get(merchantStoreGroupMapping.getMerchantStoreGroupId()):null;
                merchantStoreVO.setGroupName(ObjectUtil.isNull(group)?"":group.getName());
                merchantStoreVO.setGroupId(ObjectUtil.isNull(group)?null:group.getId());
            }
            MerchantDeliveryAddressResultDTO merchantAddressDTO = merchantAddressDtoMap.get(e.getId());
            if (Objects.nonNull(merchantAddressDTO)) {
                merchantStoreVO.setProvince(merchantAddressDTO.getProvince());
                merchantStoreVO.setArea(merchantAddressDTO.getArea());
                merchantStoreVO.setCity(merchantAddressDTO.getCity());
            }
            return merchantStoreVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Long> selectIdListByParam(Long tenantId, String storeNo, String storeName,Integer storeType, Long storeGroupId) {
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        if (Objects.nonNull(storeGroupId)) {
            List<MerchantStoreGroupResultResp> list = merchantStoreGroupMappingService.selectByGroupId(Collections.singletonList(storeGroupId), tenantId);
            if(CollectionUtil.isEmpty(list)){
                return Collections.emptyList();
            }
            merchantStoreQueryReq.setStoreIdList(list.stream().map(MerchantStoreGroupResultResp::getStoreId).collect(Collectors.toList()));
        }
        merchantStoreQueryReq.setTenantId(tenantId);
        merchantStoreQueryReq.setStoreNo(storeNo);
        merchantStoreQueryReq.setStoreName(storeName);
        merchantStoreQueryReq.setType(storeType);
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
        if(CollectionUtil.isEmpty(merchantStoreList)){
            return Collections.EMPTY_LIST;
        }
        return merchantStoreList.stream().map(MerchantStoreResultResp::getId).collect(Collectors.toList());
//        return merchantStoreMapper.selectIdListByParam(tenantId,storeName,storeNo, null, null);
    }

    @Override
    public List<String> listAddress(Long tenantId) {
        if(ObjectUtil.isEmpty(tenantId)){
            return null;
        }
        return userCenterMerchantAddressFacade.getConcatAddress(tenantId);
//        return merchantAddressMapper.selectByTenantId(tenantId);
    }

    @Override
    public List<MerchantStore> selectByTenantId(Long tenantId) {
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setTenantId(tenantId);
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
        if(CollectionUtil.isEmpty(merchantStoreList)){
            return Collections.EMPTY_LIST;
        }
        return MerchantStoreMapperConvert.INSTANCE.respListToMerchantStoreList(merchantStoreList);
    }

    @Override
    public MerchantStore selectByStoreName(Long tenantId, String storeName) {
        List<MerchantStore> merchantStores = selectByStoreNameLike(tenantId, storeName);
        if (CollectionUtil.isEmpty(merchantStores)) {
            return null;
        }
        MerchantStore merchantStore = merchantStores.stream().filter(store -> Objects.equals(store.getStoreName(), storeName)).findFirst().orElse(null);
        return merchantStore;
    }

    @Override
    public List<MerchantStore> selectByStoreNameLike(Long tenantId, String storeName) {
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setTenantId(tenantId);
        merchantStoreQueryReq.setStoreName(storeName);
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
        return MerchantStoreMapperConvert.INSTANCE.respListToMerchantStoreList(merchantStoreList);
    }

    @Override
    public PageInfo<MerchantStoreDTO> pageList(MerchantStoreQueryDTO storeQueryDTO) {
        MerchantStorePageQueryReq queryReq = MerchantStoreMapperConvert.INSTANCE.queryDtoToQueryReq(storeQueryDTO);
        Boolean placeOrderDeadlineFlag = storeQueryDTO.getPlaceOrderDeadlineFlag ();
        if(ObjectUtil.isNotNull (placeOrderDeadlineFlag) && placeOrderDeadlineFlag){
            TenantNumberCommonConfigVO tenantNumberCommonConfigVO = tenantCommonConfigService.queryTenantConfig (storeQueryDTO.getTenantId (), TenantConfigEnum.TenantConfig.PLACE_ORDER_PERMISSION_EXPIRY_TIME.getConfigKey ());
            queryReq.setPlaceOrderPermissionExpiryTime (tenantNumberCommonConfigVO.getConfigValue ());
        }
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(storeQueryDTO.getPageIndex());
        pageQueryReq.setPageSize(storeQueryDTO.getPageSize());
        PageInfo<MerchantStorePageResultResp> merchantStorePage = userCenterMerchantStoreFacade.getMerchantStorePage(queryReq, pageQueryReq);
        List<MerchantStoreDTO> merchantStoreDTOS = MerchantStoreMapperConvert.INSTANCE.pageRespListToMerchantStoreDtoList(merchantStorePage.getList());
        if(CollectionUtil.isNotEmpty (merchantStoreDTOS)){
            merchantStoreDTOS.stream().filter (e->ObjectUtil.isNotNull (e.getPlaceOrderPermissionExpiryTime ())).forEach (e->e.setPlaceOrderEnableFlag (e.getPlaceOrderPermissionExpiryTime ().compareTo (LocalDateTime.now ()) >=0));
        }
        PageInfo<MerchantStoreDTO> merchantStoreDTOPageInfo = PageInfoConverter.toPageInfoTransfer(merchantStorePage, merchantStoreDTOS);
        return merchantStoreDTOPageInfo;
    }

    @Override
    public List<MerchantStoreDTO> listAll(MerchantStoreQueryDTO storeQueryDTO) {
        storeQueryDTO.setPageIndex(NumberConstant.ONE);
        storeQueryDTO.setPageSize(Integer.MAX_VALUE);
        return pageList(storeQueryDTO).getList();
    }

    @Override
    public List<MerchantStoreDTO> selectList(Long tenantId, List<Long> storeIds) {
        MerchantStoreQueryReq merchantStoreQueryReq = new MerchantStoreQueryReq();
        merchantStoreQueryReq.setStoreIdList(storeIds);
        merchantStoreQueryReq.setTenantId(tenantId);
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(merchantStoreQueryReq);
        List<MerchantStoreDTO> merchantStoreDTOS = MerchantStoreMapperConvert.INSTANCE.respListToMerchantStoreDtoList(merchantStoreList);
        if (CollectionUtil.isEmpty(merchantStoreDTOS)) {
            return merchantStoreDTOS;
        }
        List<MerchantStoreGroupResultResp> merchantStoreGroupResultResps = merchantStoreGroupService.selectByStoreIds(storeIds, tenantId);
        Map<Long, String> groupMap = merchantStoreGroupResultResps.stream().collect(Collectors.toMap(MerchantStoreGroupResultResp::getStoreId, MerchantStoreGroupResultResp::getMerchantStoreGroupName, (v1, v2) -> v1));
        for (MerchantStoreDTO merchantStoreDTO : merchantStoreDTOS) {
            merchantStoreDTO.setGroupName(groupMap.get(merchantStoreDTO.getId()));
        }
        return merchantStoreDTOS;
    }

    @Override
    public Integer selectCountByTenantId(Long tenantId) {
        return userCenterTenantFacade.getStoreCount(tenantId);
    }

    @Override
    public List<MerchantStoreAddressDTO> listStoreIdAndAddress(Long tenantId,List<Long> storeIds) {
        if(ObjectUtil.isEmpty(tenantId)){
            return Collections.emptyList ();
        }
        List<MerchantAddress> merchantAddresses = merchantAddressMapper.listStoreIdAndAddress (tenantId,storeIds);
        if(CollectionUtils.isEmpty(merchantAddresses)){
            log.info("没有门店，tenantId={},storeIds={}",tenantId,storeIds);
            return Collections.emptyList ();
        }

        Set<String> cityNames = merchantAddresses.stream ().map (MerchantAddress::getCity).collect (Collectors.toSet ());
        List<CommonLocationCityDTO> commonLocationCityDTOS = locationCityService.queryByCityNames (cityNames);
        if(CollectionUtils.isEmpty(commonLocationCityDTOS)){
            log.info("没有commonLocationCityDTOS，tenantId={},storeIds={}",tenantId,storeIds);
            return Collections.emptyList ();
        }

        Map<String, Long> cityNameMap = commonLocationCityDTOS.stream().collect(Collectors.toMap(CommonLocationCityDTO::getName,CommonLocationCityDTO::getId));

        return merchantAddresses.stream ().map (e->{
            MerchantStoreAddressDTO dto = new MerchantStoreAddressDTO ();
            Long cityId = cityNameMap.get (e.getCity ());
            if(ObjectUtil.isNull (cityId)){
                return null;
            }
            String city = e.getCity ();
            dto.setStoreId(e.getStoreId ());
            dto.setProvince(e.getProvince ());
            dto.setCity(city);
            dto.setArea(e.getArea ());
            dto.setCityId(cityId);
            return dto;
        }).filter (ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    @Override
    public MerchantStoreAddressDTO getStoreIdAndAddress(Long tenantId,Long storeId) {
        MerchantAddressResultResp merchantAddresses = merchantAddressService.selectByStoreId(tenantId, storeId);
        if(ObjectUtil.isEmpty(merchantAddresses)){
            return null;
        }
        List<CommonLocationCityDTO> commonLocationCityDTOS = locationCityService.queryByCityNames (Collections.singleton (merchantAddresses.getCity()));
        if(CollectionUtils.isEmpty(commonLocationCityDTOS)){
            log.error ("getStoreIdAndAddress时城市查询异常，城市不存在！，storeId={},city={}",storeId,merchantAddresses.getCity());
            return null;
        }

        CommonLocationCityDTO city = commonLocationCityDTOS.get(0);

        MerchantStoreAddressDTO dto = new MerchantStoreAddressDTO ();
        dto.setStoreId(storeId);
        dto.setProvince(merchantAddresses.getProvince ());
        dto.setCity(city.getName ());
        dto.setArea(merchantAddresses.getArea ());
        dto.setCityId(city.getId ());
        return dto;
    }

    @Override
    public void updateStoreInfo(UpdateStoreInfoReqDTO storeDTO) {
        if(CollectionUtils.isEmpty(storeDTO.getStoreIds())){
            return;
        }

        for (Long storeId : storeDTO.getStoreIds()) {
            MerchantStoreCommandReq req = new MerchantStoreCommandReq();
            req.setId(storeId);
            req.setBillSwitch(storeDTO.getBillSwitch());
            Boolean flag = userCenterMerchantStoreFacade.updateMerchantStore(req);
        }
    }
}
