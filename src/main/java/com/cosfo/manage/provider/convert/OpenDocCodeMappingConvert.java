package com.cosfo.manage.provider.convert;

import com.cosfo.manage.client.open.resp.OpenDocCodeMappingResp;
import com.cosfo.manage.open.model.po.OpenDocCodeMapping;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OpenDocCodeMappingConvert {
    OpenDocCodeMappingConvert INSTANCE = Mappers.getMapper(OpenDocCodeMappingConvert.class);

    OpenDocCodeMappingResp entity2Resp(OpenDocCodeMapping openDocCodeMapping);
}