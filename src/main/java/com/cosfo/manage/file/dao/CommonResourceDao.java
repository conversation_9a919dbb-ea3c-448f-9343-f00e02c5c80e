package com.cosfo.manage.file.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.manage.file.model.dto.QueryPicResourceDTO;
import com.cosfo.manage.file.model.po.CommonResource;

import java.util.List;

/**
 * 文件资源表(CommonResource)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-25 21:03:51
 */
public interface CommonResourceDao extends IService<CommonResource> {

    void saveBatchResource(List<CommonResource> resourceList);

    Page<CommonResource> pageCommonResource(QueryPicResourceDTO dto);

}
