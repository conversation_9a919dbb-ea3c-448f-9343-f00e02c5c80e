package com.cosfo.manage.file.service;

import net.xianmu.common.result.CommonResult;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/16 16:43
 */
public interface FileDownloadRecordService {

    /**
     * 生成文件下载记录
     * @param fileDownloadRecord
     */
//    Long generateFileDownloadRecord(FileDownloadRecord fileDownloadRecord);

    /**
     * 更新文件下载记录
     * @param fileDownloadRecord
     */
//    void updateSelectiveByPrimaryKey(FileDownloadRecord fileDownloadRecord);

    /**
     * 删除文件
     * @param id
     */
//    void delete(Long id);

    /**
     * 查询所有文件
     * @param tenantId
     * @param type
     * @return
     */
//    List<FileDownloadRecord> listAll(Long tenantId, Integer type);

    /**
     * 删除过期文件
     */
//    void deleteExpiredFileTask();

    /**
     * 类型
     *
     * @param type
     * @return
     */
    CommonResult<String> downloadTemplate(Integer type);

    /**
     * 重新下载
     * @param id
     * @return
     */
//    CommonResult reDownload(Long id);

    /**
     * 刷新状态
     * @param id
     * @return
     */
//    CommonResult refresh(Long id);

    /**
     * 更新状态为失败
     * @param id
     */
//    void updateFailStatus(Long id);
}
