package com.cosfo.manage.file.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.file.model.dto.DownloadTemplateDTO;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 文件管理
 * @description 文件下载记录
 * <AUTHOR>
 * @date 2022/9/12 12:37
 */
@RestController
@RequestMapping("/file-download-record")
public class FileDownloadRecordController extends BaseController {

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;

    /**
     * 查询下载记录
     * @return
     */
//    @RequestMapping(value = "/query/list-all", method = RequestMethod.POST)
//    public CommonResult<List<FileDownloadRecord>> listAll(@RequestBody FileDownloadRecordVO fileDownloadRecordVO) {
//        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
//        List<FileDownloadRecord> fileDownloadRecordList = fileDownloadRecordService.listAll(merchantInfoDTO.getTenantId(), fileDownloadRecordVO.getType());
//        return CommonResult.ok(fileDownloadRecordList);
//    }

    /**
     * 删除下载记录
     * @return
     */
//    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
//    @RequestMapping(value = "/delete", method = RequestMethod.POST)
//    public CommonResult delete(@RequestBody FileDownloadRecordVO fileDownloadRecordVO) {
////        fileDownloadRecordService.delete(fileDownloadRecordVO.getId());
//        return CommonResult.ok();
//    }

    /**
     * 导入模板
     *
     * @param downloadTemplateDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/query/import-template")
    public CommonResult<String> downloadTemplate(@RequestBody DownloadTemplateDTO downloadTemplateDTO) {
        return fileDownloadRecordService.downloadTemplate(downloadTemplateDTO.getType());
    }

    /**
     * 重新下载
     *
     * @param fileDownloadRecordVO
     * @return
     */
//    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
//    @PostMapping("/query/redownload")
//    public CommonResult reDownload(@RequestBody FileDownloadRecordVO fileDownloadRecordVO) {
//        return fileDownloadRecordService.reDownload(fileDownloadRecordVO.getId());
//    }

    /**
     * 刷新状态
     *
     * @param fileDownloadRecordVO
     * @return
     */
//    @PostMapping("/query/refresh")
//    public CommonResult refresh(@RequestBody FileDownloadRecordVO fileDownloadRecordVO) {
//        return fileDownloadRecordService.refresh(fileDownloadRecordVO.getId());
//    }
}
