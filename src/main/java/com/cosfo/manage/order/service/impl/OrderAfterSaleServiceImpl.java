package com.cosfo.manage.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.req.MarketClassificationQueryReq;
import com.cofso.item.client.resp.MarketClassificationResp;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.DateUtil;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.bill.mapper.PaymentMapper;
import com.cosfo.manage.bill.model.dto.PaymentItemDTO;
import com.cosfo.manage.bill.model.po.PaymentCombinedDetail;
import com.cosfo.manage.bill.model.vo.BillOrderAfterSaleVO;
import com.cosfo.manage.bill.service.PaymentCombinedDetailService;
import com.cosfo.manage.common.config.OrderAfterSaleAgentSelfReviewConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.DeliveryTypeEnum;
import com.cosfo.manage.common.context.OrderStatusEnum;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.common.context.TenantConfigEnum.TenantConfig;
import com.cosfo.manage.common.context.warehouse.WarehouseQueryEnum;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.*;
import com.cosfo.manage.facade.MarketClassificationFacade;
import com.cosfo.manage.facade.OfcAfterSaleFacade;
import com.cosfo.manage.facade.ProductFacade;
import com.cosfo.manage.facade.WarehouseStorageQueryFacade;
import com.cosfo.manage.facade.category.CategoryServiceFacade;
import com.cosfo.manage.facade.dto.FulfillmentDeliveryInfoDTO;
import com.cosfo.manage.facade.dto.FulfillmentInboundDTO;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.facade.ordercenter.*;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreAccountFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.market.model.dto.MarketItemDTO;
import com.cosfo.manage.market.model.dto.MarketItemQueryDTO;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreAccount;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalance;
import com.cosfo.manage.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.manage.merchant.service.*;
import com.cosfo.manage.order.convert.OrderAfterSaleConvert;
import com.cosfo.manage.order.convert.OrderConvert;
import com.cosfo.manage.order.mapper.OrderItemMapper;
import com.cosfo.manage.order.mapper.OrderItemSnapshotMapper;
import com.cosfo.manage.order.model.dto.*;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleAuditDTO;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleBizDTO;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleBizDTO.DeliveryInfo;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleBizDTO.ResentOutboundInfo;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleBizDTO.ReturnGoodsInboundInfo;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleBizDTO.ReturnGoodsInfo;
import com.cosfo.manage.order.model.dto.aftersale.OrderAfterSaleQueryDTO;
import com.cosfo.manage.order.model.po.OrderItem;
import com.cosfo.manage.order.model.po.OrderItemSnapshot;
import com.cosfo.manage.order.model.vo.*;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleInfoVO;
import com.cosfo.manage.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.cosfo.manage.order.service.OrderAfterSaleService;
import com.cosfo.manage.supplier.service.SupplierService;
import com.cosfo.manage.tenant.dao.TenantReturnAddressDao;
import com.cosfo.manage.tenant.model.input.TenantConfigInput;
import com.cosfo.manage.tenant.model.po.TenantAccount;
import com.cosfo.manage.tenant.model.po.TenantFundAccount;
import com.cosfo.manage.tenant.model.po.TenantReturnAddress;
import com.cosfo.manage.tenant.model.vo.TenantAccountVO;
import com.cosfo.manage.tenant.model.vo.TenantCommonConfigVO;
import com.cosfo.manage.tenant.service.*;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleRuleCommandProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.goods.client.enums.CategoryTypeEnum;
import net.summerfarm.goods.client.resp.CategoryResp;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.wnc.client.req.WarehouseBaseInfoByNoReq;
import net.summerfarm.wnc.client.resp.WarehouseBaseInfoByNoResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.io.FileUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/20
 */
@Service
@Slf4j
public class OrderAfterSaleServiceImpl implements OrderAfterSaleService {

    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Resource
    private CommonService commonService;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private OrderItemSnapshotMapper orderItemSnapshotMapper;
    @Resource
    private MerchantStoreGroupMappingService merchantStoreGroupMappingService;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private TenantAccountService tenantAccountService;
    @Resource
    private OfcAfterSaleFacade ofcAfterSaleFacade;
    @Resource
    private SupplierService supplierService;
    @Resource
    private TenantAccountSupplierMappingService tenantAccountSupplierMappingService;
    @Resource
    private AuthRoleService authRoleService;
    @Resource
    private TenantReturnAddressDao tenantReturnAddressDao;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;
    @Resource
    private MarketClassificationFacade marketClassificationFacade;
    @Resource
    private WarehouseStorageQueryFacade  warehouseStorageQueryFacade;
    @Resource
    private TenantFlowSchemeService tenantFlowSchemeService;
    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;
    @DubboReference
    private OrderAfterSaleRuleCommandProvider orderAfterSaleRuleCommandProvider;
    @Resource
    private ProductFacade productFacade;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    private OrderAfterSaleAgentSelfReviewConfig orderAfterSaleAgentSelfReviewConfig;
    @Resource
    private OrderStatisticsQueryFacade orderStatisticsQueryFacade;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;
    @Resource
    private OrderAddressQueryFacade orderAddressQueryFacade;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;
    @Resource
    private OrderAfterSaleRuleQueryFacade orderAfterSaleRuleQueryFacade;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private TenantFundAccountService tenantFundAccountService;
    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;

    @Override
    public CommonResult<PageInfo<OrderAfterSaleInfoVO>> getOrderListAfterSale(OrderAfterSaleQueryDTO afterSaleQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 处理查询条件
        Boolean flag = dealQueryConditions(afterSaleQueryDTO, loginContextInfoDTO);
        if (!flag) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>(), afterSaleQueryDTO.getPageSize()));
        }
        // 查询订单数据
        OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setTenantId(afterSaleQueryDTO.getTenantId());
        req.setOrderNo(afterSaleQueryDTO.getOrderNo());
        req.setAfterSaleOrderNo(afterSaleQueryDTO.getAfterSaleOrderNo());
        if (afterSaleQueryDTO.getStatus() != null && afterSaleQueryDTO.getStatus() == 2) {
            req.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(), OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(),
                    OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue(), OrderAfterSaleStatusEnum.REFUNDDING_GOODS.getValue()));
        } else if (afterSaleQueryDTO.getStatus() != null) {
            req.setStatusList(Lists.newArrayList(afterSaleQueryDTO.getStatus()));
        }

        req.setAfterSaleType(afterSaleQueryDTO.getAfterSaleType());
        if(afterSaleQueryDTO.getServiceType() != null) {
            req.setServiceTypeList(Lists.newArrayList(afterSaleQueryDTO.getServiceType()));
        }
        req.setStoreIds(afterSaleQueryDTO.getStoreIds());
        req.setAccountIds(afterSaleQueryDTO.getAccountIds());
        req.setStartTime(LocalDateTimeUtil.of(afterSaleQueryDTO.getStartTime()));
        req.setEndTime(LocalDateTimeUtil.of(afterSaleQueryDTO.getEndTime()));
        req.setSupplierIds(afterSaleQueryDTO.getSupplierIds());
        req.setItemIds(afterSaleQueryDTO.getItemIds());
        req.setPageNum(afterSaleQueryDTO.getPageIndex());
        req.setPageSize(afterSaleQueryDTO.getPageSize());
        req.setMaxId(afterSaleQueryDTO.getMaxPrimaryId());
        req.setWarehouseNo(afterSaleQueryDTO.getWarehouseNo());
        req.setWarehouseType(afterSaleQueryDTO.getWarehouseType());

        PageInfo<OrderAfterSaleWithOrderResp> orderAfterSaleWithOrderRespPageInfo = orderAfterSaleQueryFacade.queryPage(req);

        if (orderAfterSaleWithOrderRespPageInfo == null || org.apache.commons.collections.CollectionUtils.isEmpty(orderAfterSaleWithOrderRespPageInfo.getList())) {
            return CommonResult.ok(PageInfo.emptyPageInfo());
        }
        List<OrderAfterSaleWithOrderResp> orderAfterSaleDTOS = orderAfterSaleWithOrderRespPageInfo.getList();

        List<OrderAfterSaleInfoVO> afterSaleInfoVOS = OrderAfterSaleConvert.INSTANCE.convertResp2VOS(orderAfterSaleDTOS);
        if (CollectionUtils.isEmpty(afterSaleInfoVOS)) {
            return CommonResult.ok(PageInfo.emptyPageInfo());
        }


        // 在补充门店、账户、商城信息
        List<Long> storeIdList = afterSaleInfoVOS.stream().filter(vo -> Objects.nonNull(vo.getStoreId())).map(OrderAfterSaleInfoVO::getStoreId).collect(Collectors.toList());
        List<Long> accountIds = afterSaleInfoVOS.stream().filter(vo -> Objects.nonNull(vo.getAccountId())).map(OrderAfterSaleInfoVO::getAccountId).collect(Collectors.toList());
        List<Long> tenantIds = afterSaleInfoVOS.stream().filter(vo -> Objects.nonNull(vo.getTenantId())).map(OrderAfterSaleInfoVO::getTenantId).collect(Collectors.toList());
        List<TenantResultResp> tenantInfoList = userCenterTenantFacade.getTenantsByIds(tenantIds);
        Map<Long, TenantResultResp> tenantIdMap = tenantInfoList.stream().collect(Collectors.toMap(TenantResultResp::getId, Function.identity(), (v1, v2) -> v1));
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(storeIdList);
        Map<Long, MerchantStoreResultResp> storeIdMap = merchantStoreList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, store -> store, (v1, v2) -> v1));
        List<MerchantStoreAccountResultResp> merchantStoreAccountInfo = userCenterMerchantStoreAccountFacade.getMerchantStoreAccountInfo(accountIds);
        Map<Long, MerchantStoreAccountResultResp> accountIdMap = merchantStoreAccountInfo.stream().collect(Collectors.toMap(MerchantStoreAccountResultResp::getId, account -> account, (v1, v2) -> v1));

        // 地址信息
        List<Long> orderIds = afterSaleInfoVOS.stream().map(OrderAfterSaleInfoVO::getOrderId).collect(Collectors.toList());
        List<OrderAddressResp> orderAddressResps = orderAddressQueryFacade.queryByOrderIds(loginContextInfoDTO.getTenantId(), orderIds);
        Map<Long, OrderAddressResp> orderAddressMap = orderAddressResps.stream().collect(Collectors.toMap(OrderAddressResp::getOrderId, item -> item));
        List<Long> storeIds = afterSaleInfoVOS.stream().map(OrderAfterSaleInfoVO::getStoreId).collect(Collectors.toList());
        // 供应商信息
        Map<Long, SupplierInfoDTO> supplierInfoDTOMap = supplierService.batchQuerySupplierMap(loginContextInfoDTO.getTenantId(), afterSaleQueryDTO.getSupplierIds());
        // 门店分组信息
        Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(loginContextInfoDTO.getTenantId(), storeIds);
        // 查询支付单
        List<PaymentItemDTO> paymentItemDTOS = paymentMapper.querySuccessPaymentByOrderIds (orderIds,loginContextInfoDTO.getTenantId());
        Map<Long,String> tradeTypeMap = Collections.emptyMap ();
        if(CollectionUtil.isNotEmpty (paymentItemDTOS)){
            tradeTypeMap = paymentItemDTOS.stream().collect (Collectors.toMap(PaymentItemDTO::getOrderId,PaymentItemDTO::getTradeType));;
        }
        for (OrderAfterSaleInfoVO afterSaleInfoVO : afterSaleInfoVOS) {
            TenantResultResp tenantResultResp = tenantIdMap.get(afterSaleInfoVO.getTenantId());
            MerchantStoreResultResp merchantStoreResultResp = storeIdMap.get(afterSaleInfoVO.getStoreId());
            MerchantStoreAccountResultResp merchantStoreAccountResultResp = accountIdMap.get(afterSaleInfoVO.getAccountId());
            afterSaleInfoVO.setTenantName(Optional.ofNullable(tenantResultResp).map(TenantResultResp::getTenantName).orElse(null));
            afterSaleInfoVO.setStoreName(Optional.ofNullable(merchantStoreResultResp).map(MerchantStoreResultResp::getStoreName).orElse(null));
            afterSaleInfoVO.setStoreType(Optional.ofNullable(merchantStoreResultResp).map(MerchantStoreResultResp::getType).orElse(null));
            afterSaleInfoVO.setPhone(Optional.ofNullable(merchantStoreAccountResultResp).map(MerchantStoreAccountResultResp::getPhone).orElse(null));
            afterSaleInfoVO.setAccountName(Optional.ofNullable(merchantStoreAccountResultResp).map(MerchantStoreAccountResultResp::getAccountName).orElse(null));
            afterSaleInfoVO.setMerchantStoreGroupName(groupMap.get(afterSaleInfoVO.getStoreId()));
            OrderAddressResp orderAddress = orderAddressMap.get(afterSaleInfoVO.getOrderId());
            StringBuffer address = new StringBuffer();
            afterSaleInfoVO.setAddress(address.append(orderAddress.getProvince()).append(orderAddress.getArea()).append(orderAddress.getCity()).append(orderAddress.getAddress()).toString());
            SupplierInfoDTO supplierInfoDTO = supplierInfoDTOMap.get(afterSaleInfoVO.getSupplierTenantId());
            afterSaleInfoVO.setSupplierName(Objects.isNull(supplierInfoDTO) ? "" : supplierInfoDTO.getSupplierName());
            // 物流信息
            DeliveryInfo deliveryInfoData = getDeliveryInfoData(afterSaleInfoVO.getAfterSaleOrderNo());
            afterSaleInfoVO.setDeliveryInfo(deliveryInfoData);
            // 前端展示状态
            Integer frontShowStatus = getFrontShowStatus(afterSaleInfoVO.getTenantId(), afterSaleInfoVO.getStatus(), afterSaleInfoVO.getWarehouseType(), afterSaleInfoVO.getGoodsType(), afterSaleInfoVO.getServiceType (),tradeTypeMap.get (afterSaleInfoVO.getOrderId()));
            afterSaleInfoVO.setStatus(frontShowStatus);

            // ！！自营仓不展示供应商价格
            if (afterSaleInfoVO.getSupplyPrice() != null && !GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(afterSaleInfoVO.getGoodsType())) {
                BigDecimal ratio = BigDecimal.ONE;
                if (BigDecimal.ZERO.compareTo(afterSaleInfoVO.getPayablePrice()) != 0) {
                    ratio = afterSaleInfoVO.getSupplyPrice().divide(afterSaleInfoVO.getPayablePrice(), 4, RoundingMode.HALF_UP);
                }
                if (afterSaleInfoVO.getApplyPrice() != null) {
                    afterSaleInfoVO.setSupplyApplyRefundPrice(afterSaleInfoVO.getApplyPrice().multiply(ratio).setScale(2, RoundingMode.HALF_UP));
                }
                if (afterSaleInfoVO.getTotalPrice() != null) {
                    afterSaleInfoVO.setSupplyTotalRefundPrice(afterSaleInfoVO.getTotalPrice().multiply(ratio).setScale(2, RoundingMode.HALF_UP));
                }

            }
        }
        return CommonResult.ok(PageInfoHelper.pageInfoCopy(orderAfterSaleWithOrderRespPageInfo, afterSaleInfoVOS));
    }
    private Integer getFrontShowStatus(Long tenantId, Integer status, Integer warehouseType, Integer goodsType, Integer afterSaleType,String tradeType) {
        //        如果是线下支付待审核则显示待审核
        if(PaymentTradeTypeEnum.OFFLINE_PAY.getDesc ().equals (tradeType) && !Lists.newArrayList (OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue(),OrderAfterSaleServiceTypeEnum.RESEND.getValue()).contains (afterSaleType)){
            return status;
        }else {
            if (OrderAfterSaleStatusEnum.checkSubStatusForInventoryDealing(status)) {
                return OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue();
            }
            // 代仓且没开启自审加待审核显示待确认
            if (Objects.equals (goodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode ())
                    && Objects.equals (warehouseType, WarehouseTypeEnum.THREE_PARTIES.getCode ())
                    && !orderAfterSaleAgentSelfReviewConfig.getAgentAfterSaleSelfReviewFlag (tenantId)
                    && Objects.equals (status, OrderAfterSaleStatusEnum.UNAUDITED.getValue ())
            ) {
                return OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue ();
            }
            // 报价待审核显示待确认
            if (Objects.equals (goodsType, GoodsTypeEnum.QUOTATION_TYPE.getCode ())
                    && Objects.equals (warehouseType, WarehouseTypeEnum.THREE_PARTIES.getCode ())
                    && Objects.equals (status, OrderAfterSaleStatusEnum.UNAUDITED.getValue ())
            ) {
                return OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue ();
            }
        }
        return status;
    }

    @Override
    public void exportOrdersAfterSale(OrderAfterSaleQueryDTO afterSaleQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Map<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.TEN);
        // 原订单编号
        if (!com.cosfo.manage.common.util.StringUtils.isBlank(afterSaleQueryDTO.getOrderNo())) {
            queryParamsMap.put(Constants.ORDER_NO, afterSaleQueryDTO.getOrderNo());
        }
        // 售后订单状态
        if (Objects.nonNull(afterSaleQueryDTO.getStatus())) {
            queryParamsMap.put(Constants.ORDER_AFTER_SALE_STATUS, OrderAfterSaleStatusEnum.getStatusDesc(afterSaleQueryDTO.getStatus()));
        }
        // 售后类型
        if (Objects.nonNull(afterSaleQueryDTO.getAfterSaleType())) {
            queryParamsMap.put(Constants.AFTER_SALE_TYPE, OrderAfterSaleTypeEnum.getDesc(afterSaleQueryDTO.getAfterSaleType()));
        }
        // 门店类型
        if (Objects.nonNull(afterSaleQueryDTO.getStoreType())) {
            queryParamsMap.put(Constants.STORE_TYPE, MerchantStoreEnum.Type.getDesc(afterSaleQueryDTO.getStoreType()));
        }
        // 售后服务类型
        if (Objects.nonNull(afterSaleQueryDTO.getServiceType())) {
            queryParamsMap.put(Constants.SERVICE_TYPE, OrderAfterSaleServiceTypeEnum.getDesc(afterSaleQueryDTO.getServiceType()));
        }
        // 起始时间
        if (Objects.nonNull(afterSaleQueryDTO.getStartTime())) {
            queryParamsMap.put(Constants.ORDER_START_TIME, TimeUtils.changeDate2String(afterSaleQueryDTO.getStartTime(), Constants.TIME_FORMAT_LONG));
        }
        // 手机号
        if (!com.cosfo.manage.common.util.StringUtils.isBlank(afterSaleQueryDTO.getPhone())) {
            queryParamsMap.put(Constants.PHONE, afterSaleQueryDTO.getPhone());
        }
        // 售后订单编号
        if (!com.cosfo.manage.common.util.StringUtils.isBlank(afterSaleQueryDTO.getAfterSaleOrderNo())) {
            queryParamsMap.put(Constants.AFTER_SALE_ORDER_NO, afterSaleQueryDTO.getAfterSaleOrderNo());
        }
        // 截止时间
        if (Objects.nonNull(afterSaleQueryDTO.getEndTime())) {
            queryParamsMap.put(Constants.ORDER_END_TIME, TimeUtils.changeDate2String(afterSaleQueryDTO.getEndTime(), Constants.TIME_FORMAT_SHORT) + Constants.END_TIME);
        }
        // 门店名称
        if (!StringUtils.isEmpty(afterSaleQueryDTO.getStoreName())) {
            queryParamsMap.put(Constants.STORE_NAME, afterSaleQueryDTO.getStoreName());
        }
        if (!Objects.isNull(afterSaleQueryDTO.getItemId())) {
            queryParamsMap.put(Constants.ITEM_ID, afterSaleQueryDTO.getItemId().toString());
        }
        if (!StringUtils.isEmpty(afterSaleQueryDTO.getWarehouseNo())) {
            // 处理仓库编号
            WarehouseQueryEnum warehouseQueryEnum = WarehouseQueryEnum.getById(afterSaleQueryDTO.getWarehouseNo());
            afterSaleQueryDTO.setWarehouseType(transferWarehouseType(afterSaleQueryDTO, warehouseQueryEnum));
            if (Objects.nonNull(warehouseQueryEnum)) {
                queryParamsMap.put(Constants.WAREHOUSE_NAME, warehouseQueryEnum.getName());
            } else {
                WarehouseStorageResp warehouseStorageResp = warehouseStorageQueryFacade.queryOneWarehouseStorage(afterSaleQueryDTO.getWarehouseNo());
                queryParamsMap.put(Constants.WAREHOUSE_NAME, Optional.ofNullable(warehouseStorageResp).map(WarehouseStorageResp::getWarehouseName).orElse(afterSaleQueryDTO.getWarehouseNo().toString()));
            }
        }
        // 门店分组
        String groupNames = merchantStoreGroupService.queryGroupNameByIds(loginContextInfoDTO.getTenantId(), afterSaleQueryDTO.getMerchantStoreGroupIds());
        if (!StringUtils.isEmpty(groupNames)) {
            queryParamsMap.put(Constants.STORE_GROUPS, groupNames);
        }
        if (!StringUtils.isEmpty(afterSaleQueryDTO.getTitle())) {
            queryParamsMap.put(Constants.TITLE, afterSaleQueryDTO.getTitle());
        }
        if (Objects.nonNull(afterSaleQueryDTO.getSupplierId())) {
            Map<Long, SupplierInfoDTO> supplierInfoMap = supplierService.batchQuerySupplierMap(loginContextInfoDTO.getTenantId(), Arrays.asList(afterSaleQueryDTO.getSupplierId()));
            SupplierInfoDTO supplierInfoDTO = supplierInfoMap.get(afterSaleQueryDTO.getSupplierId());
            queryParamsMap.put(Constants.SUPPLIER_NAME, supplierInfoDTO.getSupplierName());
        }

        afterSaleQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());


        Boolean isSupplierDistributor = authRoleService.isSupplierRole(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getAuthUserId());

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.ORDERS_AFTER_SALE.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_AFTER_SALE_ORDERS.getFileName() : ExcelTypeEnum.AFTERSALEORDERS.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(afterSaleQueryDTO, e -> writeDownloadCenter(e, recordDTO.getFileName(), isSupplierDistributor, loginContextInfoDTO));

//        //存储文件下载记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(loginContextInfoDTO.getTenantId());
//        record.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        // 设置导出类型为订单
//        record.setType(FileDownloadTypeEnum.ORDERS_AFTER_SALE.getType());
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//
//        afterSaleQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
//        //发送导出异步消息
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                generateAfterSaleOrderFile(afterSaleQueryDTO, record.getId(), loginContextInfoDTO);
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(record.getId());
//                throw new DefaultServiceException(e);
//            }
//        });
    }


    public DownloadCenterOssRespDTO writeDownloadCenter(OrderAfterSaleQueryDTO afterSaleQueryDTO, String fileName, Boolean isSupplierDistributor, LoginContextInfoDTO loginContextInfoDTO) {
        // 1、表格处理
        String filePath = generateAfterSaleOrderFile(afterSaleQueryDTO, isSupplierDistributor, loginContextInfoDTO);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }


    private String generateAfterSaleOrderFile(OrderAfterSaleQueryDTO afterSaleQueryDTO, Boolean isSupplierDistributor, LoginContextInfoDTO loginContextInfoDTO) {
        // 处理查询条件
//        Boolean flag = dealQueryConditions(afterSaleQueryDTO, loginContextInfoDTO);
//        if (!flag) {
//            return commonService.exportExcel(new ArrayList(), isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_AFTER_SALE_ORDERS.getName() : ExcelTypeEnum.AFTERSALEORDERS.getName());
//        }
        afterSaleQueryDTO.setPageIndex(NumberConstants.ONE);
        afterSaleQueryDTO.setPageSize(NumberConstants.HUNDRED);

        PageInfo<OrderAfterSaleInfoVO> pageInfo = getOrderListAfterSale(afterSaleQueryDTO, loginContextInfoDTO).getData();
        if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
            return commonService.exportExcel(new ArrayList(), isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_AFTER_SALE_ORDERS.getName() : ExcelTypeEnum.AFTERSALEORDERS.getName());
        }
        int pages = (int) Math.max((pageInfo.getTotal() - 1) / pageInfo.getPageSize() + 1, 0);

        // 创建excelWriter
        String filePath = ExcelUtils.tempExcelFilePath();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        ExcelWriter excelWriter = EasyExcel.write(filePath, OrderAfterSaleInfoVO.class).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter())
                .withTemplate(ExcelUtils.getExcelFileInputStream(this.getClass(), isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_AFTER_SALE_ORDERS.getName() : ExcelTypeEnum.AFTERSALEORDERS.getName()))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        Long maxPrimaryId = null;
        // 分页查询
        for (int pageNum = NumberConstants.ONE; pageNum <= pages; pageNum++) {
            List<OrderAfterSaleInfoVO> orderAfterSaleInfoVOS;
            if (pageNum == NumberConstants.ONE) {
                orderAfterSaleInfoVOS = pageInfo.getList();
            } else {
                afterSaleQueryDTO.setMaxPrimaryId(maxPrimaryId);
                orderAfterSaleInfoVOS = getOrderListAfterSale(afterSaleQueryDTO, loginContextInfoDTO).getData().getList();
            }
            if (CollectionUtil.isEmpty(orderAfterSaleInfoVOS)) {
                break;
            }
            List<OrderAfterSaleInfoVO> tempList = orderAfterSaleInfoVOS.stream().map(orderAfterSaleInfoVO ->
                    convertOrderAfterSaleInfoVO(Collections.singletonList(orderAfterSaleInfoVO), orderAfterSaleInfoVO.getTenantId())).flatMap(Collection::stream).collect(Collectors.toList());
            maxPrimaryId = tempList.get(tempList.size() - 1).getId();
            // 分批写
            excelWriter.fill(tempList, fillConfig, writeSheet);
        }
        excelWriter.finish();

        return filePath;
//        commonService.uploadExcelAndUpdateDownloadStatus(filePath, null, isSupplierDistributor ? ExcelTypeEnum.SUPPLIER_AFTER_SALE_ORDERS : ExcelTypeEnum.AFTERSALEORDERS, fileDownloadRecordId);
    }

    private List<OrderAfterSaleInfoVO> convertOrderAfterSaleInfoVO(List<OrderAfterSaleInfoVO> afterSaleInfos, Long tenantId) {
        List<Long> marketItemIds = afterSaleInfos.stream().map(OrderAfterSaleInfoVO::getItemId).collect(Collectors.toList());
        Map<Long, MarketItemDTO> marketItemMap = marketItemService.getMapByItemIds(marketItemIds);
        List<Long> storeIds = afterSaleInfos.stream().map(OrderAfterSaleInfoVO::getStoreId).collect(Collectors.toList());
        Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(tenantId, storeIds);
        List<Long> orderIds = afterSaleInfos.stream().map(OrderAfterSaleInfoVO::getOrderId).collect(Collectors.toList());
        // 地址信息
        List<OrderAddressResp> orderAddressResps = orderAddressQueryFacade.queryByOrderIds(tenantId, orderIds);
        Map<Long, OrderAddressResp> orderAddressMap = orderAddressResps.stream().collect(Collectors.toMap(OrderAddressResp::getOrderId, item -> item));
        afterSaleInfos.stream().forEach(info -> {
            info.setStatusDesc(OrderAfterSaleStatusEnum.getStatusDesc(info.getStatus()));
            info.setAfterSaleTypeDesc(OrderAfterSaleTypeEnum.getDesc(info.getAfterSaleType()));
            info.setServiceTypeDesc(OrderAfterSaleServiceTypeEnum.getDesc(info.getServiceType()));
            MarketItemDTO marketItemDTO = marketItemMap.get(info.getItemId());
            info.setItemId(info.getItemId());
            info.setSkuId(Objects.equals(-1L, info.getSkuId()) ? null : info.getSkuId());
            info.setItemCode(Objects.isNull(marketItemDTO) ? "" : marketItemDTO.getItemCode());
            info.setAfterSaleUnit(OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(info.getServiceType(), info.getAfterSaleType()) ? info.getAfterSaleUnit() : info.getSpecificationUnit());
            info.setMerchantStoreGroupName(groupMap.get(info.getStoreId()));
            info.setStoreTypeDesc(MerchantStoreEnum.Type.getDesc(info.getStoreType()));
            OrderAddressResp orderAddress = orderAddressMap.get(info.getOrderId());
            StringBuffer address = new StringBuffer();
            info.setGoodsTypeDesc(GoodsTypeEnum.getShowDescByCodeV2(info.getGoodsType()));
            info.setAddress(address.append(orderAddress.getProvince()).append(orderAddress.getArea()).append(orderAddress.getCity()).append(orderAddress.getAddress()).toString());
        });
        return afterSaleInfos;
    }

//    private Pair<String, String> getWarehouseName(String warehouseNo, Integer warehouseType) {
//        String warehouseName = WarehouseTypeEnum.getByCode(warehouseType).getDesc();
//        String warehouseServiceName = org.apache.commons.lang3.StringUtils.EMPTY;
//        if (org.apache.commons.lang3.StringUtils.isBlank(warehouseNo)) {
//            return Pair.create(warehouseName, warehouseServiceName);
//
//        }
//        try {
//            WarehouseStorageResp warehouseStorageResp = ofcAfterSaleFacade.queryWarehouse(Integer.parseInt(warehouseNo));
//            warehouseName = warehouseStorageResp.getWarehouseName();
//            warehouseServiceName = warehouseStorageResp.getWarehouseServiceName();
//        } catch (Exception e) {
//            log.error("queryWarehouse error. ", e);
//        }
//        return Pair.create(warehouseName, warehouseServiceName);
//    }

    @Override
    public CommonResult<List<OrderAfterSaleProductVO>> getOrderAfterSaleCommodity(Long orderId) {
        AssertCheckParams.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        Long tenantId = UserLoginContextUtils.getTenantId();
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByOrderId(orderId, tenantId);
        AssertCheckParams.notNull(orderAfterSaleResps, ResultDTOEnum.PARAMETER_MISSING.getCode(), "售后订单不存在");
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            log.warn("售后订单不存在, orderId={}, 当前用户信息={}", orderId, JSON.toJSON(UserLoginContextUtils.getMerchantInfoDTO()));
            throw new BizException("售后订单不存在");
        }
        List<Long> orderItemIds = orderAfterSaleResps.stream().map(OrderAfterSaleResp::getOrderItemId).distinct().collect(Collectors.toList());
        List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryByOrderItemIds(orderItemIds);
        Map<Long, OrderItemSnapshotResp> orderItemSnapshotDTOMap = orderItemSnapshotResps.stream().collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, Function.identity(), (v1, v2) -> v1));

        List<OrderItemResp> orderItemResps = orderItemQueryFacade.queryByIds(orderItemIds);
        Map<Long, OrderItemResp> orderItemDTOMap = orderItemResps.stream().collect(Collectors.toMap(OrderItemResp::getId, Function.identity(), (v1, v2) -> v1));
        Map<Long, CategoryResp> skuIdCategoryRespMap = queryItemCategoryType(orderItemSnapshotResps);
        //获取退货仓名称信息
        Map<String, String> returnWarehouseNameMap = batchQueryReturnWarehouseName(orderAfterSaleResps);

        // 待审核无仓退货退款获取退货地址信息
        ReturnGoodsInfo returnGoodsInfo = queryReturnGoodsInfo(orderAfterSaleResps);

        Map<String, ReturnGoodsInfo> returnGoodsInfoMap = queryReturnGoodsInfoMap(orderAfterSaleResps);
        // 无货补发物流信息
        Map<String, DeliveryInfo> deliveryInfoMap = queryAfterSaleDeliveryInfo(orderAfterSaleResps);
        // 查询自营补发物流信息
        Map<String, List<ResentOutboundInfo>> resentOutboundInfoMap = queryResentOutboundInfo(orderAfterSaleResps, orderItemDTOMap);
        // 无仓退货地址
        Map<Long, ReturnGoodsInfo> noGoodsReturnGoodsInfoMap = queryNoGoodsReturnGoodsInfoMap(orderAfterSaleResps);
        //查询支付单
        List<PaymentItemDTO> paymentItemDTOS = paymentMapper.querySuccessPaymentByOrderIds (Collections.singletonList (orderId),tenantId);
        String tradeType;
        if(CollectionUtil.isNotEmpty (paymentItemDTOS)){
            tradeType = paymentItemDTOS.get (0).getTradeType ();
        } else {
            tradeType = null;
        }
        List<OrderAfterSaleBizDTO> orderAfterSaleBizDTOS = orderAfterSaleResps.stream().map(dto -> {
            OrderAfterSaleBizDTO sale = OrderAfterSaleConvert.INSTANCE.resp2BizDto(dto);

            Long orderItemId = dto.getOrderItemId();
            sale.setSkuId(orderItemSnapshotDTOMap.get(orderItemId).getSkuId());
            sale.setAfterSaleUnit(orderItemSnapshotDTOMap.get(orderItemId).getAfterSaleUnit());
            sale.setSpecificationUnit(orderItemSnapshotDTOMap.get(orderItemId).getSpecificationUnit());
            sale.setSupplyPrice(orderItemSnapshotDTOMap.get(orderItemId).getSupplyPrice());
            sale.setPayablePrice(orderItemDTOMap.get(orderItemId).getPayablePrice());

            fillAfterSaleInfo(sale, returnWarehouseNameMap, returnGoodsInfo, deliveryInfoMap, resentOutboundInfoMap, returnGoodsInfoMap, noGoodsReturnGoodsInfoMap);

            Integer goodsType = orderItemSnapshotDTOMap.get(orderItemId).getGoodsType();
            Integer frontShowStatus = getFrontShowStatus(sale.getTenantId(), sale.getStatus(), sale.getWarehouseType(), goodsType,sale.getServiceType  (),tradeType);
            sale.setStatus(frontShowStatus);

            sale.setAfterSaleUnit(OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(sale.getServiceType(), sale.getAfterSaleType()) ? sale.getAfterSaleUnit() : sale.getSpecificationUnit());
            // 获取下单账号信息
            MerchantStoreAccountVO merchantStoreAccountVO = merchantStoreAccountService.queryAccountInfo(sale.getAccountId());
            StringBuffer accountName = new StringBuffer(merchantStoreAccountVO.getAccountName())
                    .append("(").append(merchantStoreAccountVO.getPhone()).append(")");
            sale.setAccountName(accountName.toString());

            if (sale.getSupplyPrice() != null) {
                BigDecimal ratio = BigDecimal.ONE;
                if (BigDecimal.ZERO.compareTo(sale.getPayablePrice()) != 0) {
                    ratio = sale.getSupplyPrice().divide(sale.getPayablePrice(), 4, RoundingMode.HALF_UP);
                }
                if (sale.getApplyPrice() != null) {
                    sale.setSupplyApplyRefundPrice(sale.getApplyPrice().multiply(ratio).setScale(2, RoundingMode.HALF_UP));
                }
                if (sale.getTotalPrice() != null) {
                    sale.setSupplyTotalRefundPrice(sale.getTotalPrice().multiply(ratio).setScale(2, RoundingMode.HALF_UP));
                }

            }
            return sale;
        }).collect(Collectors.toList());

        List<OrderAfterSaleProductVO> resultVo = new ArrayList<>();
        Map<Long, List<OrderAfterSaleBizDTO>> orderAfterSaleMap = orderAfterSaleBizDTOS.stream().collect(Collectors.groupingBy(OrderAfterSaleBizDTO::getOrderItemId));
        List<Long> marketItemIds = orderItemResps.stream().map(OrderItemResp::getItemId).collect(Collectors.toList());
        Map<Long, MarketItemDTO> marketItemMap = marketItemService.getMapByItemIds(marketItemIds);
        for (Map.Entry<Long, List<OrderAfterSaleBizDTO>> entry : orderAfterSaleMap.entrySet()) {
            Long orderItemId = entry.getKey();
            List<OrderAfterSaleBizDTO> bizDTOList = entry.getValue();
            OrderItemSnapshotResp dto = orderItemSnapshotDTOMap.get(orderItemId);
            if(dto == null){
                continue;
            }
            OrderAfterSaleProductVO afterSaleProductVo = new OrderAfterSaleProductVO();
            afterSaleProductVo.setMainPicture(dto.getMainPicture());
            afterSaleProductVo.setOrderNo(orderItemDTOMap.get(orderItemId).getOrderId() + "");
            afterSaleProductVo.setItemId(orderItemDTOMap.get(orderItemId).getItemId());
            Integer categoryType = getCategoryType(orderItemSnapshotDTOMap, skuIdCategoryRespMap, orderItemId);
            afterSaleProductVo.setCategoryType(categoryType);
            afterSaleProductVo.setTitle(dto.getTitle());
            afterSaleProductVo.setWarehouseType(dto.getWarehouseType());
            afterSaleProductVo.setDeliveryType(dto.getDeliveryType());
            afterSaleProductVo.setAfterSaleOrders(bizDTOList.stream().sorted(Comparator.comparing(OrderAfterSaleBizDTO::getCreateTime).reversed()).collect(Collectors.toList()));
            MarketItemDTO marketItemDTO = marketItemMap.get(orderItemDTOMap.get(orderItemId).getItemId());
            afterSaleProductVo.setItemCode(Objects.isNull(marketItemDTO) ? "" : marketItemDTO.getItemCode());
            afterSaleProductVo.setGoodsType(Objects.isNull(marketItemDTO) ? -1 : marketItemDTO.getGoodsType());
            resultVo.add(afterSaleProductVo);
        }

        return CommonResult.ok(resultVo);
    }

    private Integer getCategoryType(Map<Long, OrderItemSnapshotResp> orderItemSnapshotDTOMap, Map<Long, CategoryResp> skuIdCategoryRespMap, Long orderItemId) {
        Integer categoryType = OrderProductCategoryTypeEnum.DEFAULT.getType();
        Long skuId = Optional.ofNullable(orderItemSnapshotDTOMap.get(orderItemId)).map(OrderItemSnapshotResp::getSkuId).orElse(null);
        if (Objects.isNull(skuId)) {
            return categoryType;
        }

        // 填充订单商品项所属货品分类类型
        CategoryResp categoryResp = skuIdCategoryRespMap.get(skuId);
        if (Objects.nonNull(categoryResp) && Objects.nonNull(categoryResp.getType())) {
            categoryType = OrderProductCategoryTypeEnum.STANDARD.getType();
            if (CategoryTypeEnum.FRUIT.getValue().equals(categoryResp.getType())) {
                categoryType = OrderProductCategoryTypeEnum.FRUITS.getType();
            }
        }
        return categoryType;
    }

    /**
     * 查询订单商品所属货品分类
     * @param orderItemSnapshotDTOS
     * @return
     */
    private Map<Long, CategoryResp> queryItemCategoryType(List<OrderItemSnapshotResp> orderItemSnapshotDTOS) {
        if (CollectionUtil.isEmpty(orderItemSnapshotDTOS)) {
            return Collections.emptyMap();
        }
        List<Long> skuIds = orderItemSnapshotDTOS.stream().filter(snapshot ->
                Objects.nonNull(snapshot.getSkuId()) && snapshot.getSkuId() > NumberConstant.ZERO)
                .map(OrderItemSnapshotResp::getSkuId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        List<ProductSkuDetailResp> productSkuDetailResps = productFacade.selectProductSkuDetailById(skuIds);
        List<Long> categoryIds = productSkuDetailResps.stream().filter(resp -> Objects.nonNull(resp.getCategoryId()))
                .map(ProductSkuDetailResp::getCategoryId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(categoryIds)) {
            return Collections.emptyMap();
        }
        List<CategoryResp> categoryResps = categoryServiceFacade.selectCategoryDetail(categoryIds);
        Map<Long, CategoryResp> categoryRespMap = categoryResps.stream().collect(Collectors.toMap(CategoryResp::getId, Function.identity(), (v1, v2) -> v1));

        Map<Long, CategoryResp> skuCategoryMap = productSkuDetailResps.stream().filter(sku ->
                Objects.nonNull(categoryRespMap.get(sku.getCategoryId())))
                .collect(Collectors.toMap(sku -> sku.getSkuId(), sku ->
                        categoryRespMap.get(sku.getCategoryId()), (v1, v2) -> v1));
        return skuCategoryMap;
    }

    private void fillAfterSaleInfo(OrderAfterSaleBizDTO afterSaleBizDTO,
                                   Map<String, String> returnWarehouseNameMap,
                                   ReturnGoodsInfo returnGoodsInfo,
                                   Map<String, DeliveryInfo> deliveryInfoMap,
                                   Map<String, List<ResentOutboundInfo>> resentOutboundInfoMap,
                                   Map<String, ReturnGoodsInfo> returnGoodsInfoMap,
                                   Map<Long, ReturnGoodsInfo> noGoodsReturnGoodsInfoMap) {

        Integer status = afterSaleBizDTO.getStatus();
        Integer warehouseType = afterSaleBizDTO.getWarehouseType();
        Integer serviceType = afterSaleBizDTO.getServiceType();
        String afterSaleOrderNo = afterSaleBizDTO.getAfterSaleOrderNo();

        // 售后状态文案
        afterSaleBizDTO.setStatusDesc(OrderAfterSaleStatusEnum.getDetailStatusDesc(status, serviceType, warehouseType));

        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType)) {
            return;
        }

        if (afterSaleBizDTO.getReturnWarehouseNo() != null) {
//                afterSaleBizDTO.setReturnWarehouseName(getWarehouseName(afterSaleBizDTO.getReturnWarehouseNo(), afterSaleBizDTO.getWarehouseType()).getFirst());
            afterSaleBizDTO.setReturnWarehouseName(returnWarehouseNameMap.get(afterSaleBizDTO.getReturnWarehouseNo()));
        }

        // 补充无货退货地址信息
        if (OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(status)) {
            if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType) && OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(serviceType)) {
                afterSaleBizDTO.setReturnGoodsInfo(returnGoodsInfo);
            }
        }

        // 填充补发-物流信息、出库信息
        if (OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(serviceType)) {
            if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType)) {
                afterSaleBizDTO.setDeliveryInfo(deliveryInfoMap.get(afterSaleOrderNo));
            } else if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
                afterSaleBizDTO.setResentOutboundInfoList(resentOutboundInfoMap.get(afterSaleOrderNo));
            }
        }
        // 退货退款-补充物流
        // 非三方仓 非UNAUDITED 非WAIT_REFUND_GOODS
        if (OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(serviceType) && !WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType)) {
            if (!OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(status) && !OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue().equals(status)) {
                afterSaleBizDTO.setDeliveryInfo(deliveryInfoMap.get(afterSaleOrderNo));
            }
        }
        // 非待审核 自营仓退货地址
        if (!OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(status) && OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(serviceType)) {
           if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
               afterSaleBizDTO.setReturnGoodsInfo(returnGoodsInfoMap.get(afterSaleBizDTO.getReturnWarehouseNo()));
           }
        }
        // 非待审核 无货退货地址
        if (!OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(status) && OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(serviceType)) {
            if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType)) {
                afterSaleBizDTO.setReturnGoodsInfo(noGoodsReturnGoodsInfoMap.get(afterSaleBizDTO.getReturnAddressId()));
            }
        }

        // TODO:[zhoujiachen] 暂不支持批量，查询次数较少，后续优化
        // 填充自营仓退货入库信息
        if (OrderAfterSaleStatusEnum.isShowReturnGoodsInboundInfo(status, serviceType, warehouseType)) {
            afterSaleBizDTO.setReturnGoodsInboundInfo(getReturnGoodsInboundInfo(afterSaleBizDTO.getAfterSaleOrderNo()));
        }
    }

    /**
     * 查询自营补发物流信息
     * @param afterSales
     * @param orderItemDTOMap
     * @return
     */
    private Map<String, List<ResentOutboundInfo>> queryResentOutboundInfo(List<OrderAfterSaleResp> afterSales, Map<Long, OrderItemResp> orderItemDTOMap) {
        List<String> afterSaleNos = afterSales.stream().filter(dto -> {
            if (!OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(dto.getServiceType())) {
                return false;
            }
            return WarehouseTypeEnum.PROPRIETARY.getCode().equals(dto.getWarehouseType());
        }).map(OrderAfterSaleResp::getAfterSaleOrderNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afterSaleNos)) {
            return Collections.emptyMap();
        }
        List<FulfillmentDeliveryInfoDTO> response = ofcAfterSaleFacade.queryDelivery(afterSaleNos);
        if (CollectionUtils.isEmpty(response)) {
            return Collections.emptyMap();
        }
        Map<String, Long> afterSaleNoOrderItemMap = afterSales.stream().filter(orderAfterSaleDTO -> afterSaleNos.contains(orderAfterSaleDTO.getAfterSaleOrderNo())).collect(Collectors.toMap(OrderAfterSaleResp::getAfterSaleOrderNo, OrderAfterSaleResp::getOrderItemId));
        return response.stream().filter(res -> {
            Long orderItemId = afterSaleNoOrderItemMap.get(res.getOrderNo());
            OrderItemResp orderItemDTO = orderItemDTOMap.get(orderItemId);
            if (res.getItemId() == null || !res.getItemId().equals(String.valueOf(orderItemDTO.getItemId()))) {
                return false;
            }
            return true;
        }).map(res -> {
            ResentOutboundInfo resentOutboundInfo = new ResentOutboundInfo();
            resentOutboundInfo.setDeliveryType(res.getDeliveryType());
            // 配送方式，0-无需物流，1-商家物流配送，2-仓库物流配送
            if (res.getDeliveryType().equals(2)) {
                resentOutboundInfo.setDeliveryType(1);
            }
            resentOutboundInfo.setDeliveryCompany(res.getLogisticsCompany());
            resentOutboundInfo.setDeliveryNo(res.getLogisticsNo());
            resentOutboundInfo.setRemark(res.getRemark());
            if (resentOutboundInfo.getDeliveryType().equals(1)) {
                resentOutboundInfo.setJumpLink(String.format(Constants.DELIVERY_QUERY_URL, res.getLogisticsCompany(), res.getLogisticsNo()));
            }
            resentOutboundInfo.setBatchNo(res.getBatchNo());
            resentOutboundInfo.setOutBoundNo(res.getOutBoundNo());
            resentOutboundInfo.setItemId(res.getItemId());
            resentOutboundInfo.setQuantity(res.getQuantity());
            resentOutboundInfo.setWarehouseNo(res.getWarehouseNo());
            resentOutboundInfo.setWarehouseName(res.getWarehouseName());
            resentOutboundInfo.setWarehouseServiceName(res.getWarehouseServiceName());
            resentOutboundInfo.setOrderNo(res.getOrderNo());
            return resentOutboundInfo;
        }).collect(Collectors.groupingBy(ResentOutboundInfo::getOrderNo));
    }

    /**
     * 无仓补发 查询售后单补发物流信息
     * @param afterSales
     * @return
     */
    private Map<String, DeliveryInfo> queryAfterSaleDeliveryInfo(List<OrderAfterSaleResp> afterSales) {
        List<String> afterSaleNos = afterSales.stream().filter(dto->{
            // 无货补发
            if (OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(dto.getServiceType()) && WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(dto.getWarehouseType())) {
                return true;
            }
            // 非三方仓
            // 退货退款-补充物流
            // 非三方仓 非UNAUDITED 非WAIT_REFUND_GOODS
            if (OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(dto.getServiceType()) && !WarehouseTypeEnum.THREE_PARTIES.getCode().equals(dto.getWarehouseType())) {
                if (!OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(dto.getStatus()) && !OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue().equals(dto.getStatus())) {
                    return true;
                }
            }
            return false;
        }).map(OrderAfterSaleResp::getAfterSaleOrderNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afterSaleNos)) {
            return Collections.emptyMap();
        }
        return queryDeliveryInfo(afterSaleNos);
    }


    private Map<String, DeliveryInfo> queryDeliveryInfo(List<String> afterSaleNos) {
        List<FulfillmentDeliveryInfoDTO> response = ofcAfterSaleFacade.queryDelivery(afterSaleNos);
        if (CollectionUtils.isEmpty(response)) {
            return Collections.emptyMap();
        }
        return response.stream().map(res -> {
            DeliveryInfo deliveryInfo = new DeliveryInfo();
            deliveryInfo.setDeliveryType(res.getDeliveryType());
            if (Integer.valueOf(2).equals(res.getDeliveryType()) || Integer.valueOf(3).equals(res.getDeliveryType())) {
                deliveryInfo.setDeliveryType(1);
            }
            deliveryInfo.setDeliveryCompany(res.getLogisticsCompany());
            deliveryInfo.setDeliveryNo(res.getLogisticsNo());
            deliveryInfo.setRemark(res.getRemark());
            if (deliveryInfo.getDeliveryType().equals(1)) {
                deliveryInfo.setJumpLink(String.format(Constants.DELIVERY_QUERY_URL, res.getLogisticsCompany(), res.getLogisticsNo()));
            }
            deliveryInfo.setPics(res.getPics());
            deliveryInfo.setCreateTime(res.getCreateTime());
            deliveryInfo.setBatchNo(res.getBatchNo());
            deliveryInfo.setOrderNo(res.getOrderNo());
            return deliveryInfo;
        }).collect(Collectors.toMap(DeliveryInfo::getOrderNo, d -> d));
    }

    private Map<String, ReturnGoodsInfo> queryReturnGoodsInfoMap(List<OrderAfterSaleResp> afterSales) {
        // 自营仓 非 待审核
        Map<String, ReturnGoodsInfo> goodsInfoMap = new HashMap<>();
        List<Long> orderIds = afterSales.stream().filter(afterSale->{
            if (!OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(afterSale.getStatus()) && OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(afterSale.getServiceType())) {
                if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(afterSale.getWarehouseType())) {
                    return true;
                }
            }
            return false;
        }).map(OrderAfterSaleResp::getOrderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return goodsInfoMap;
        }
        List<OrderResp> orderResps = orderQueryFacade.queryByIds(orderIds);
        List<Integer> warehouseNos = orderResps.stream().map(OrderResp::getWarehouseNo).map(Integer::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseNos)) {
            return goodsInfoMap;
        }
        List<WarehouseStorageResp> warehouseStorageResps = warehouseStorageQueryFacade.queryWarehouseStorageList(null, null, warehouseNos);
        if (CollectionUtils.isEmpty(warehouseStorageResps)) {
            return goodsInfoMap;
        }
        for (WarehouseStorageResp warehouseStorageResp : warehouseStorageResps) {
            ReturnGoodsInfo returnGoodsInfo = new ReturnGoodsInfo();
            returnGoodsInfo.setAddress(warehouseStorageResp.getAddress());
            returnGoodsInfo.setContact(warehouseStorageResp.getPersonContact());
            returnGoodsInfo.setMobile(warehouseStorageResp.getPhone());
            goodsInfoMap.put(warehouseStorageResp.getWarehouseNo().toString(), returnGoodsInfo);
        }
        return goodsInfoMap;
    }

    private Map<Long, ReturnGoodsInfo> queryNoGoodsReturnGoodsInfoMap(List<OrderAfterSaleResp> afterSales) {
        // 非待审核 退货退款 无货
        List<Long> returnAddressIds = afterSales.stream().filter(afterSale->{
            if (OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(afterSale.getStatus())) {
                return false;
            }
            if (!WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(afterSale.getWarehouseType())) {
                return false;
            }
            if (!OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(afterSale.getServiceType())) {
                return false;
            }
            return true;
        }).map(OrderAfterSaleResp::getReturnAddressId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(returnAddressIds)) {
            return Collections.emptyMap();
        }
        List<TenantReturnAddress> tenantReturnAddresses = tenantReturnAddressDao.queryByIds(returnAddressIds);
        if (CollectionUtils.isEmpty(tenantReturnAddresses)) {
            return Collections.emptyMap();
        }
        return tenantReturnAddresses.stream().map(address->{
            ReturnGoodsInfo returnGoodsInfo = new ReturnGoodsInfo();
            returnGoodsInfo.setReturnAddressId(address.getId());
            StringBuilder addressStr = new StringBuilder()
                    .append(address.getProvince())
                    .append(address.getCity())
                    .append(address.getArea())
                    .append(address.getAddress())
                    .append(address.getHouseNo() == null ? "" : address.getHouseNo());
            returnGoodsInfo.setAddress(addressStr.toString());
            returnGoodsInfo.setContact(address.getContactName());
            returnGoodsInfo.setMobile(address.getContactPhone());
            return returnGoodsInfo;
        }).collect(Collectors.toMap(ReturnGoodsInfo::getReturnAddressId, Function.identity(), (v1, v2) -> v1));
    }


    private ReturnGoodsInfo queryReturnGoodsInfo(List<OrderAfterSaleResp> afterSales) {
        //待审核无仓退货退款
        Optional<OrderAfterSaleResp> any = afterSales.stream().filter(dto -> {
            if (!OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(dto.getStatus())) {
                return false;
            }
            if (!WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(dto.getWarehouseType())) {
                return false;
            }
            if (!OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(dto.getServiceType())) {
                return false;
            }
            return true;
        }).findAny();
        if (!any.isPresent()) {
            return null;
        }
        OrderAfterSaleResp orderAfterSaleDTO = any.get();
        ReturnAddressVO returnAddressVO = null;
        Long returnAddressId = orderAfterSaleQueryFacade.getRecentlyUsedReturnAddressId(orderAfterSaleDTO.getTenantId());
        if (returnAddressId != null) {
            returnAddressVO = tenantReturnAddressDao.getByPrimaryKey(returnAddressId);
        }
        if (returnAddressVO == null) {
            returnAddressVO = tenantReturnAddressDao.getRecentlyUsedAddress(orderAfterSaleDTO.getTenantId());
        }
        if (returnAddressVO != null) {
            ReturnGoodsInfo returnGoodsInfo = new ReturnGoodsInfo();
            StringBuilder address = new StringBuilder()
                    .append(returnAddressVO.getProvince())
                    .append(returnAddressVO.getCity())
                    .append(returnAddressVO.getArea())
                    .append(returnAddressVO.getAddress())
                    .append(returnAddressVO.getHouseNo() == null ? "" : returnAddressVO.getHouseNo());
            returnGoodsInfo.setReturnAddressId(returnAddressVO.getId());
            returnGoodsInfo.setAddress(address.toString());
            returnGoodsInfo.setContact(returnAddressVO.getContactName());
            returnGoodsInfo.setMobile(returnAddressVO.getContactPhone());
            return returnGoodsInfo;
        }
        return null;
    }

    /**
     * 批量查询退货仓名称
     * @param afterSales
     * @return
     */
    private Map<String, String> batchQueryReturnWarehouseName(List<OrderAfterSaleResp> afterSales) {
        Map<String, String> returnWarehouseNameMap = new HashMap<>();
        Map<Integer, List<String>> returnWarehouseNoMap = afterSales.stream().filter(dto->!StringUtils.isEmpty(dto.getReturnWarehouseNo())).collect(Collectors.groupingBy(OrderAfterSaleResp::getWarehouseType, Collectors.mapping(OrderAfterSaleResp::getReturnWarehouseNo, Collectors.toList())));
        for (Map.Entry<Integer, List<String>> entry : returnWarehouseNoMap.entrySet()) {
            Integer warehouseType = entry.getKey();
            String warehouseName = WarehouseTypeEnum.getByCode(warehouseType).getDesc();
            List<String> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            WarehouseBaseInfoByNoReq req = new WarehouseBaseInfoByNoReq();
            req.setWarehouseNos(value.stream().map(Integer::parseInt).collect(Collectors.toList()));
            List<WarehouseBaseInfoByNoResp> warehouseBaseInfoByNoResps = warehouseStorageQueryFacade.queryBaseInfoByWarehouseNo(req);
            Map<Integer, String> nameMap = warehouseBaseInfoByNoResps.stream().collect(Collectors.toMap(WarehouseBaseInfoByNoResp::getWarehouseNo, WarehouseBaseInfoByNoResp::getWarehouseName));
            for (String no : value) {
                returnWarehouseNameMap.put(no, nameMap.getOrDefault(Integer.parseInt(no), warehouseName));
            }
        }
        return returnWarehouseNameMap;
    }


//    private void fillDataForOrderAfterSaleDTO(OrderAfterSaleBizDTO orderAfterSaleBizDTO) {
//        Integer warehouseType = orderAfterSaleBizDTO.getWarehouseType();
//        Integer status = orderAfterSaleBizDTO.getStatus();
//        Integer afterSaleType = orderAfterSaleBizDTO.getAfterSaleType();
//        Integer serviceType = orderAfterSaleBizDTO.getServiceType();
//
//        // 售后状态文案
//        orderAfterSaleBizDTO.setStatusDesc(OrderAfterSaleStatusEnum.getDetailStatusDesc(status, serviceType, warehouseType));
//
//        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType)) {
//            return;
//        }
//
////        if (OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(status)) {
////            if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType) && OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(serviceType)) {
////                ReturnAddressVO returnAddressVO = null;
////                Long returnAddressId = RpcResultUtil.handle(orderAfterSaleQueryService.getRecentlyUsedReturnAddressId(orderAfterSaleBizDTO.getTenantId()));
////                if (returnAddressId != null) {
////                    returnAddressVO = tenantReturnAddressDao.getByPrimaryKey(returnAddressId);
////                }
////                if (returnAddressVO == null) {
////                    returnAddressVO = tenantReturnAddressDao.getRecentlyUsedAddress(orderAfterSaleBizDTO.getTenantId());
////                }
////                if (returnAddressVO != null) {
////                    ReturnGoodsInfo returnGoodsInfo = new ReturnGoodsInfo();
////                    StringBuilder address = new StringBuilder()
////                            .append(returnAddressVO.getProvince())
////                            .append(returnAddressVO.getCity())
////                            .append(returnAddressVO.getArea())
////                            .append(returnAddressVO.getAddress())
////                            .append(returnAddressVO.getHouseNo() == null ? "" : returnAddressVO.getHouseNo());
////                    returnGoodsInfo.setReturnAddressId(returnAddressVO.getId());
////                    returnGoodsInfo.setAddress(address.toString());
////                    returnGoodsInfo.setContact(returnAddressVO.getContactName());
////                    returnGoodsInfo.setMobile(returnAddressVO.getContactPhone());
////                    orderAfterSaleBizDTO.setReturnGoodsInfo(returnGoodsInfo);
////                }
////            }
////            return;
////        }
//
//        // 填充补发-物流信息、出库信息
////        if (OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(serviceType)) {
////            if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType)) {
////                DeliveryInfo deliveryInfo = getDeliveryInfoData(orderAfterSaleBizDTO.getAfterSaleOrderNo());
////                orderAfterSaleBizDTO.setDeliveryInfo(deliveryInfo);
////            } else if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
////                fillData2ResentOutboundInfoList(orderAfterSaleBizDTO);
////            }
////        }
//        // TODO:[zhoujiachen] 暂不支持批量，查询次数较少，后续优化
//        // 填充自营仓退货入库信息
//        if (OrderAfterSaleStatusEnum.isShowReturnGoodsInboundInfo(status, serviceType, warehouseType)) {
//            orderAfterSaleBizDTO.setReturnGoodsInboundInfo(getReturnGoodsInboundInfo(orderAfterSaleBizDTO.getAfterSaleOrderNo()));
//        }
//
//        // 填充退货 买家物流信息
//        if (!OrderAfterSaleStatusEnum.UNAUDITED.getValue().equals(status) && OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(serviceType)) {
//            fillData2DeliverInfoReturnGoods(orderAfterSaleBizDTO);
//        }
//    }

    /**
     * 填充退货退款信息 买家退货地址，买家退货物流、备注、凭证等
     *
     * @param orderAfterSaleBizDTO
     */
//    private void fillData2DeliverInfoReturnGoods(OrderAfterSaleBizDTO orderAfterSaleBizDTO) {
//        // 填充退货-退回地址信息
////        if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(orderAfterSaleBizDTO.getWarehouseType())) {
////            ReturnAddressVO returnAddressVO = tenantReturnAddressDao.getByPrimaryKey(orderAfterSaleBizDTO.getReturnAddressId());
////            if (returnAddressVO != null) {
////                ReturnGoodsInfo returnGoodsInfo = new ReturnGoodsInfo();
////                StringBuilder address = new StringBuilder()
////                        .append(returnAddressVO.getProvince())
////                        .append(returnAddressVO.getCity())
////                        .append(returnAddressVO.getArea())
////                        .append(returnAddressVO.getAddress())
////                        .append(returnAddressVO.getHouseNo() == null ? "" : returnAddressVO.getHouseNo());
////                returnGoodsInfo.setAddress(address.toString());
////                returnGoodsInfo.setContact(returnAddressVO.getContactName());
////                returnGoodsInfo.setMobile(returnAddressVO.getContactPhone());
////                orderAfterSaleBizDTO.setReturnGoodsInfo(returnGoodsInfo);
////            }
////        }
////        } else {
////            orderAfterSaleBizDTO.setReturnGoodsInfo(getReturnAddress(orderAfterSaleBizDTO.getOrderId(), orderAfterSaleBizDTO.getReturnWarehouseNo()));
////        }
////        if (!OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue().equals(orderAfterSaleBizDTO.getStatus())) {
////            DeliveryInfo deliveryInfo = new DeliveryInfo();
////            // 填充退货-买家物流，买家退货凭证，备注、退货时间
////            orderAfterSaleBizDTO.setDeliveryInfo(deliveryInfo);
////
////            FulfillmentDeliveryInfoDTO dto = getDeliveryInfo(orderAfterSaleBizDTO.getAfterSaleOrderNo());
////            if (dto != null) {
////                deliveryInfo.setDeliveryType(dto.getDeliveryType());
////                if (dto.getDeliveryType().equals(3)) {
////                    deliveryInfo.setDeliveryType(1);
////                }
////                deliveryInfo.setDeliveryCompany(dto.getLogisticsCompany());
////                deliveryInfo.setDeliveryNo(dto.getLogisticsNo());
////                deliveryInfo.setRemark(dto.getRemark());
////                // 配送方式 1物流快递
////                if (deliveryInfo.getDeliveryType().equals(1)) {
////                    deliveryInfo.setJumpLink(String.format(Constants.DELIVERY_QUERY_URL, dto.getLogisticsCompany(), dto.getLogisticsNo()));
////                }
////                deliveryInfo.setPics(dto.getPics());
////                deliveryInfo.setCreateTime(dto.getCreateTime());
////            }
////        }
//    }

    /**
     * 填充无仓补发退货物流信息
     *
     * @param afterSaleOrderNo
     */
    private DeliveryInfo getDeliveryInfoData(String afterSaleOrderNo) {
        FulfillmentDeliveryInfoDTO dto = getDeliveryInfo(afterSaleOrderNo);
        DeliveryInfo deliveryInfo = new DeliveryInfo();
        if (dto != null) {
            deliveryInfo.setDeliveryType(dto.getDeliveryType());
            // 配送方式，0-无需物流，1-商家物流配送，2-仓库物流配送
            if (dto.getDeliveryType().equals(2)) {
                deliveryInfo.setDeliveryType(1);
            }
            deliveryInfo.setDeliveryCompany(dto.getLogisticsCompany());
            deliveryInfo.setDeliveryNo(dto.getLogisticsNo());
            deliveryInfo.setRemark(dto.getRemark());
            if (deliveryInfo.getDeliveryType().equals(1)) {
                deliveryInfo.setJumpLink(String.format(Constants.DELIVERY_QUERY_URL, dto.getLogisticsCompany(), dto.getLogisticsNo()));
            }
            deliveryInfo.setBatchNo(dto.getBatchNo());
            return deliveryInfo;
        }
        return null;
    }

    private ReturnGoodsInboundInfo getReturnGoodsInboundInfo(String afterSaleNo) {
        ReturnGoodsInboundInfo returnGoodsInboundInfo = new ReturnGoodsInboundInfo();
        try {
            FulfillmentInboundDTO fulfillmentInboundDTO = ofcAfterSaleFacade.queryInboundInfo(afterSaleNo);
            if (fulfillmentInboundDTO != null) {
                returnGoodsInboundInfo.setInBoundTime(fulfillmentInboundDTO.getInBoundTime());
                if (org.apache.commons.lang3.StringUtils.isNotBlank(fulfillmentInboundDTO.getInboundPics())) {
                    returnGoodsInboundInfo.setInboundPics(fulfillmentInboundDTO.getInboundPics());
                }
                returnGoodsInboundInfo.setQuantity(fulfillmentInboundDTO.getQuantity());
                returnGoodsInboundInfo.setActualQuantity(fulfillmentInboundDTO.getActualQuantity());
                if (org.apache.commons.lang3.StringUtils.isNotBlank(fulfillmentInboundDTO.getReason())) {
                    returnGoodsInboundInfo.setReason(fulfillmentInboundDTO.getReason());
                }
                returnGoodsInboundInfo.setInboundStatus(fulfillmentInboundDTO.getInboundStatus());
            }
        } catch (Exception e) {
            log.error("queryInboundInfo error. ", e);
        }
        return returnGoodsInboundInfo;
    }

    private FulfillmentDeliveryInfoDTO getDeliveryInfo(String afterSaleNo) {
        FulfillmentDeliveryInfoDTO dto = null;
        List<FulfillmentDeliveryInfoDTO> deliveryInfoDTOlist = null;
        try {
            deliveryInfoDTOlist = ofcAfterSaleFacade.queryDelivery(Lists.newArrayList(afterSaleNo));
        } catch (Exception e) {
            log.error("queryDelivery error. ", e);
        }
        if (!CollectionUtils.isEmpty(deliveryInfoDTOlist)) {
            dto = deliveryInfoDTOlist.get(0);
        }
        return dto;
    }


//    private List<FulfillmentDeliveryInfoDTO> getResentDeliveryInfo(String afterSaleNo) {
//        List<FulfillmentDeliveryInfoDTO> deliveryInfoDTOlist = new ArrayList<>();
//        try {
//            deliveryInfoDTOlist = ofcAfterSaleFacade.queryDelivery(Lists.newArrayList(afterSaleNo));
//        } catch (Exception e) {
//            log.error("getResentDeliveryInfo error. ", e);
//        }
//        return deliveryInfoDTOlist;
//    }

    @Override
    public CommonResult<OrderInfoVo> getOrderCommodity(Long orderId) {
        AssertCheckParams.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        OrderInfoVo orderInfoVo = new OrderInfoVo();
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        OrderVO orderVO = OrderConvert.INSTANCE.convertResp2VO(orderResp);
        if (orderVO == null) {
            throw new BizException("订单不存在");
        }
        OrderTenantIsolateCheckUtil.check(orderVO);
        // 获取订单项信息
        List<OrderItemVO> orderItemVOS = orderItemQueryFacade.queryOrderItemByOrderId(orderId);
        List<Long> itemIds = orderItemVOS.stream().map(OrderItemVO::getItemId).collect(Collectors.toList());
        Map<Long, MarketItemDTO> marketItemMap = marketItemService.getMapByItemIds(itemIds);

        BigDecimal productTotalPrice = BigDecimal.ZERO;
        for (OrderItemVO orderItemVO : orderItemVOS) {
            productTotalPrice = NumberUtil.add(productTotalPrice, orderItemVO.getTotalPrice());
            MarketItemDTO marketItemDTO = marketItemMap.get(orderItemVO.getItemId());
            orderItemVO.setItemCode(Objects.isNull(marketItemDTO) ? "" : marketItemDTO.getItemCode());
        }
        orderInfoVo.setProductTotalPrice(productTotalPrice);
        orderInfoVo.setDeliveryFee(orderVO.getDeliveryFee());
        orderInfoVo.setTotalPrice(orderVO.getTotalPrice());
        orderInfoVo.setPayablePrice(orderVO.getPayablePrice());

        orderInfoVo.setOrderItemVOS(orderItemVOS);
        return CommonResult.ok(orderInfoVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult reviewSubmissions(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO requestContextInfoDTO) {
        // 数据校验
        OrderAfterSaleResp afterSale = orderAfterSaleQueryFacade.getOrderAfterSaleByNo(orderAfterSaleAuditDTO.getAfterSaleOrderNo());
        OrderResp orderResp = orderQueryFacade.queryById(afterSale.getOrderId());
        List<Integer> needAuditStatusList = Arrays.asList(OrderAfterSaleStatusEnum.UNAUDITED.getValue(), OrderAfterSaleStatusEnum.WAIT_REFUND.getValue());
        if (!needAuditStatusList.contains(afterSale.getStatus())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.STATUS_NOT_AUDITING.getMessage());
        }
        if (Objects.isNull(orderAfterSaleAuditDTO.getTotalPrice())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.TOTAL_PRICE_EMPTY.getMessage());
        }
        if (orderAfterSaleAuditDTO.getTotalPrice().compareTo(BigDecimal.ZERO) == NumberConstants.NEGATIVE_ONE) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.TOTAL_PRICE_NEGATIVE.getMessage());
        }
        if (orderAfterSaleAuditDTO.getTotalPrice().compareTo(afterSale.getApplyPrice()) == NumberConstants.ONE) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.TOTAL_PRICE_TOO_MUCH.getMessage());
        }

        if(!canAuditAfterSaleOrder(afterSale, requestContextInfoDTO.getAuthUserId())){
            throw new BizException("该售后订单您暂无审核权限，请联系管理员");
        }

        orderAfterSaleAuditDTO.setOperatorName(requestContextInfoDTO.getTenantName());
        TenantAccountVO tenantAccountVO = tenantAccountService.getSaasTenantAccountVO(requestContextInfoDTO.getAuthUserId());
        if (tenantAccountVO != null) {
            orderAfterSaleAuditDTO.setOperatorName(tenantAccountVO.getNickname());
        }

        OrderAfterSaleAuditReq req = new OrderAfterSaleAuditReq();
        req.setSystemSource(SystemSourceEnum.MANAGE.getCode());
        req.setAfterSaleOrderNo(orderAfterSaleAuditDTO.getAfterSaleOrderNo());
        req.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
        req.setAuditStatus(orderAfterSaleAuditDTO.getAuditStatus());
        req.setRecycleTime(orderAfterSaleAuditDTO.getRecycleTime());
        req.setAmount(orderAfterSaleAuditDTO.getAmount());
        req.setTotalPrice(orderAfterSaleAuditDTO.getTotalPrice());
        req.setOperatorName(orderAfterSaleAuditDTO.getOperatorName());
        req.setResponsibilityType(orderAfterSaleAuditDTO.getResponsibilityType());

        req.setDeliveryType(orderAfterSaleAuditDTO.getDeliveryType());
        req.setDeliveryCompany(orderAfterSaleAuditDTO.getDeliveryCompany());
        req.setDeliveryNo(orderAfterSaleAuditDTO.getDeliveryNo());
        req.setRemark(orderAfterSaleAuditDTO.getRemark());
        req.setQuantity(orderAfterSaleAuditDTO.getQuantity());
        req.setActualQuantity(orderAfterSaleAuditDTO.getActualQuantity());
        req.setReturnAddressId(orderAfterSaleAuditDTO.getReturnAddressId());
        req.setReturnWarehouseNo(orderAfterSaleAuditDTO.getReturnWarehouseNo());
        req.setSupplierApplyPrice(orderAfterSaleAuditDTO.getSupplierApplyPrice());
        req.setSupplierTotalRefundPrice(orderAfterSaleAuditDTO.getSupplierTotalRefundPrice());
        req.setRefundReceipt(orderAfterSaleAuditDTO.getRefundReceipt());

        Long tenantId = afterSale.getTenantId();
        Long storeId = afterSale.getStoreId();
        Long fundAccountId = orderAfterSaleAuditDTO.getTenantFundAccountId();
        Long balanceId = getBalanceAccountId(tenantId, storeId, fundAccountId);
        req.setBalanceId(balanceId);

        Boolean flag = RpcResultUtil.handle(orderAfterSaleCommandProvider.reviewSubmissions(req));
        if (!flag) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后单审核失败");
        }

        return CommonResult.ok();
    }

    /**
     * 指定租户维度的非现金子账户，通过门店ID查询如有则直接返回，没有则初始化账号
     * @param tenantId
     * @param storeId
     * @param fundAccountId
     * @return
     */
    private Long getBalanceAccountId(Long tenantId, Long storeId, Long fundAccountId) {
        if (fundAccountId == null) {
            return null;
        }
        List<TenantFundAccount> tenantFundAccounts = tenantFundAccountService.listByIds(Collections.singletonList(fundAccountId));
        if (CollectionUtils.isEmpty(tenantFundAccounts)) {
            return null;
        }
        MerchantStoreBalance merchantStoreBalance = merchantStoreBalanceService.queryByStoreFundAccountId(tenantId, storeId, fundAccountId);
        if (merchantStoreBalance != null) {
            return merchantStoreBalance.getId();
        }
        List<MerchantStoreResultResp> merchantStoreList = userCenterMerchantStoreFacade.getMerchantStoreList(Collections.singletonList(storeId));
        if (CollectionUtils.isEmpty(merchantStoreList)) {
            return null;
        }
        MerchantStoreResultResp merchantStoreResultResp = merchantStoreList.get(0);
        MerchantStoreBalance initMerchantStoreBalance = new MerchantStoreBalance();
        initMerchantStoreBalance.setTenantId(tenantId);
        initMerchantStoreBalance.setStoreId(storeId);
        initMerchantStoreBalance.setStoreNo(merchantStoreResultResp.getStoreNo());
        initMerchantStoreBalance.setBalance(BigDecimal.ZERO);
        initMerchantStoreBalance.setAccountType(MerchantStoreBalanceEnums.AccountTypeEnum.NON_CASH.getType());
        initMerchantStoreBalance.setFundAccountId(fundAccountId);
        Long initBalanceId = merchantStoreBalanceService.save(initMerchantStoreBalance);
        log.info("初始化门店非现金账户成功,门店id={},余额id={}", storeId, initBalanceId);
        return initBalanceId;
    }

    private boolean canAuditAfterSaleOrder(OrderAfterSaleResp afterSale, Long authUserId) {
        OrderItemSnapshotResp orderItemSnapshotResp = orderItemSnapshotQueryFacade.queryByOrderItemId(afterSale.getOrderItemId());
        Integer goodsType = orderItemSnapshotResp.getGoodsType();

        // 配送前售后
        boolean notsendFlag = Objects.equals(afterSale.getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType());
        // 配送后售后
        boolean deliveredFlag = Objects.equals(afterSale.getAfterSaleType(), OrderAfterSaleTypeEnum.DELIVERED.getType());
        // 代仓商品
        boolean agentWarehouseFlag = Objects.equals(afterSale.getWarehouseType(), com.cosfo.ordercenter.client.common.WarehouseTypeEnum.THREE_PARTIES.getCode()) && Objects.equals(goodsType, com.cofso.item.client.enums.GoodsTypeEnum.SELF_GOOD_TYPE.getCode());
        // 无仓或自营仓
        boolean selfWarehouseFlag = Objects.equals(afterSale.getWarehouseType(), WarehouseTypeEnum.NO_WAREHOUSE.getCode()) || Objects.equals(afterSale.getWarehouseType(), WarehouseTypeEnum.PROPRIETARY.getCode());

        if (notsendFlag && agentWarehouseFlag) {
            return tenantFlowSchemeService.canAuditByAccountIdAndBizType(authUserId, afterSale.getStoreId(), FlowRuleAuditBizTypeEnum.AGENT_WAREHOUSE_PRE_DELIVERY_AFTERSALE_AUDIT);
        }

        if (notsendFlag && selfWarehouseFlag) {
            return tenantFlowSchemeService.canAuditByAccountIdAndBizType(authUserId, afterSale.getStoreId(), FlowRuleAuditBizTypeEnum.SELFWAREHOUSE_PRE_DELIVERY_AFTERSALE_AUDIT);
        }

        return true;
    }


    @Override
    public CommonResult modifyQuantity(OrderAfterSaleUnAuditModifyQuantityDTO orderAfterSaleUnAuditModifyQuantityDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Integer quantity = orderAfterSaleUnAuditModifyQuantityDTO.getQuantity();
        if (quantity == null || quantity <= 0) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后数量不能为0");
        }
        OrderAfterSaleResp afterSale = orderAfterSaleQueryFacade.getOrderAfterSaleByNo(orderAfterSaleUnAuditModifyQuantityDTO.getAfterSaleOrderNo());
        if (afterSale == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后订单不存在");
        }
        OrderResp orderResp = orderQueryFacade.queryById(afterSale.getOrderId());
        if (orderResp == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "订单不存在");
        }

        // 权限校验
        checkModifyQuantityPermission(afterSale);

        // 状态校验
        if (!Objects.equals(afterSale.getStatus(), OrderAfterSaleStatusEnum.UNAUDITED.getValue()) && !Objects.equals(afterSale.getStatus(), OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "状态非待审核或者待确认");
        }

        // 当前售后订单锁定的售后数量
        Integer afterSaleAmount = afterSale.getAmount();
        // 当前售后订单锁定的售后金额
        BigDecimal afterSaleApplyPrice = afterSale.getApplyPrice();

        // 校验可售后额度
        OrderAfterSaleEnableResp orderAfterSaleEnableApplyDTO = orderAfterSaleQueryFacade.queryEnableApply(afterSale.getTenantId(), afterSale.getOrderId(), afterSale.getOrderItemId())
                .get(afterSale.getOrderItemId());
        Integer enableApplyQuantity = orderAfterSaleEnableApplyDTO.getEnableApplyQuantity();
        Integer enableApplyAmount = orderAfterSaleEnableApplyDTO.getEnableApplyAmount();
        BigDecimal enableApplyPrice = orderAfterSaleEnableApplyDTO.getEnableApplyPrice();
        log.info("enableApplyQuantity：{}, enableApplyPrice：{}", enableApplyQuantity, enableApplyPrice);
        if (enableApplyQuantity < 0) {
            throw new DefaultServiceException("可售后件数不足");
        }
        if (enableApplyPrice.compareTo(BigDecimal.ZERO) < 0) {
            throw new DefaultServiceException("可售后金额不足,最大可售后金额为" + enableApplyPrice + "元");
        }

        BigDecimal applyPrice = BigDecimal.ZERO;

        boolean isReceivedRefund = OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(afterSale.getServiceType(), afterSale.getAfterSaleType());
        if (isReceivedRefund) {
            if (quantity.compareTo(enableApplyQuantity + afterSaleAmount) > 0) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "可售后件数不足");
            }
            OrderItemSnapshot orderItemSnapshot = orderItemSnapshotMapper.selectByItemId(afterSale.getTenantId(), afterSale.getOrderItemId());
            OrderItem orderItem = orderItemMapper.selectByPrimaryKey(afterSale.getOrderItemId());
            if (orderItemSnapshot == null || orderItem == null) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "订单不存在");
            }

            // 已配送售后 退款-申请售后金额，按照最大售后数量
            applyPrice = NumberUtil.div(NumberUtil.mul(quantity, orderItem.getPayablePrice()), orderItemSnapshot.getMaxAfterSaleAmount(), 2);

        } else {
            if (quantity.compareTo(enableApplyAmount + afterSaleAmount) > 0) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "可售后件数不足");
            }

            // 非【已配送售后退款】全部按照件数计算金额。补发和换货不计算售后金额，为0
            if (OrderAfterSaleServiceTypeEnum.verifyIsRefund(afterSale.getServiceType())) {
                OrderItem orderItem = orderItemMapper.selectByPrimaryKey(afterSale.getOrderItemId());
                // 退货退款-申请售后金额
                applyPrice = NumberUtil.mul(orderItem.getPayablePrice(), quantity);
            }
        }
        if (applyPrice.compareTo(NumberUtil.add(enableApplyPrice, afterSaleApplyPrice)) > 0) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "可售后金额不足,最大可售后金额为" + enableApplyPrice + "元");
        }

        OrderAfterSaleModifyQuantityReq req = new OrderAfterSaleModifyQuantityReq();
        req.setAfterSaleId(afterSale.getId());
        req.setOperatorName(loginContextInfoDTO.getTenantName());
        req.setAmount(quantity);
        req.setApplyPrice(applyPrice);
        req.setTotalPrice(applyPrice);
        DubboResponse<Boolean> response = orderAfterSaleCommandProvider.modifyQuantity(req);

        log.info("modifyQuantity 修改售后数量 OrderAfterSale update={}", req);

        return CommonResult.ok();
    }

    private void checkModifyQuantityPermission(OrderAfterSaleResp afterSale) {
        OrderItemSnapshotResp orderItemSnapshotResp = orderItemSnapshotQueryFacade.queryByOrderItemId(afterSale.getOrderItemId());
        Integer goodsType = orderItemSnapshotResp.getGoodsType();
        boolean agentWarehouseFlag = Objects.equals(afterSale.getWarehouseType(), com.cosfo.ordercenter.client.common.WarehouseTypeEnum.THREE_PARTIES.getCode()) && Objects.equals(goodsType, com.cofso.item.client.enums.GoodsTypeEnum.SELF_GOOD_TYPE.getCode());
        boolean agentAfterSaleSelfReviewFlag = orderAfterSaleAgentSelfReviewConfig.getAgentAfterSaleSelfReviewFlag(afterSale.getTenantId());
        if (agentWarehouseFlag && !agentAfterSaleSelfReviewFlag) {
            // 代仓售后且品牌方没开启自审配置
            throw new BizException("该售后订单您暂无改数量权限");
        }
        boolean quotationFlag = Objects.equals(afterSale.getWarehouseType(), com.cosfo.ordercenter.client.common.WarehouseTypeEnum.THREE_PARTIES.getCode()) && Objects.equals(goodsType, com.cofso.item.client.enums.GoodsTypeEnum.QUOTATION_TYPE.getCode());
        if (quotationFlag) {
            throw new BizException("该售后订单您暂无改数量权限");
        }
    }

    @Override
    public CommonResult updateAdminRemark(OrderAfterSaleRemarkDTO orderAfterSaleRemarkDTO, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.hasText(orderAfterSaleRemarkDTO.getAdminRemark(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "备注不能为空");

        OrderAfterSaleResp afterSale = orderAfterSaleQueryFacade.getOrderAfterSaleByNo(orderAfterSaleRemarkDTO.getAfterSaleOrderNo());
        if (afterSale == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后订单不存在");
        }

        OrderAfterSaleUpdateReq updateReq = new OrderAfterSaleUpdateReq();
        updateReq.setId(afterSale.getId());
        updateReq.setAdminRemark(orderAfterSaleRemarkDTO.getAdminRemark());
        updateReq.setAdminRemarkTime(LocalDateTime.now());
        RpcResultUtil.handle(orderAfterSaleCommandProvider.updateById(updateReq));

        return CommonResult.ok();
    }

    @Override
    public CommonResult updateDeliveryInfo(OrderAfterSaleDeliveryDTO orderAfterSaleDeliveryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        AssertCheckParams.hasText(orderAfterSaleDeliveryDTO.getAfterSaleOrderNo(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "售后单号不能为空");

        OrderAfterSaleResp afterSale = orderAfterSaleQueryFacade.getOrderAfterSaleByNo(orderAfterSaleDeliveryDTO.getAfterSaleOrderNo());

        if (afterSale == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后订单不存在");
        }

        if (!WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(afterSale.getWarehouseType())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "不支持修改");
        }

        String operator = loginContextInfoDTO.getTenantName();
        TenantAccountVO tenantAccountVO = tenantAccountService.getSaasTenantAccountVO(loginContextInfoDTO.getAuthUserId());
        if (tenantAccountVO != null) {
            operator = tenantAccountVO.getNickname();
        }
        // RPC调用ofc接口更新物流信息
        ofcAfterSaleFacade.updateDeliveryInfo(orderAfterSaleDeliveryDTO, operator);

        return CommonResult.ok();
    }

    /**
     * 处理查询条件
     *
     * @param orderAfterSaleQueryDTO
     */
    private Boolean dealQueryConditions(OrderAfterSaleQueryDTO orderAfterSaleQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        orderAfterSaleQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        // 供应商
        populateSupplierQueryCondition(orderAfterSaleQueryDTO, loginContextInfoDTO);
        // 门店类型和门店账号
        List<Long> queryStoreIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderAfterSaleQueryDTO.getMerchantStoreGroupIds())) {
            List<MerchantStoreGroupResultResp> mappings = merchantStoreGroupMappingService.selectByGroupId(orderAfterSaleQueryDTO.getMerchantStoreGroupIds(), loginContextInfoDTO.getTenantId());
            if (CollectionUtils.isEmpty(mappings)) {
                return Boolean.FALSE;
            }
            queryStoreIds = mappings.stream().map(MerchantStoreGroupResultResp::getStoreId).collect(Collectors.toList());
        }
        if (orderAfterSaleQueryDTO.getStoreType() != null || !StringUtils.isEmpty(orderAfterSaleQueryDTO.getStoreName())) {
            MerchantStoreQueryDTO query = MerchantStoreQueryDTO.builder()
                    .tenantId(loginContextInfoDTO.getTenantId())
                    .type(orderAfterSaleQueryDTO.getStoreType())
                    .storeName(orderAfterSaleQueryDTO.getStoreName())
                    .storeIds(queryStoreIds)
                    .build();
            List<MerchantStoreDTO> merchantStoreDTOS = merchantStoreService.listByConditionNew(query);
            if (CollectionUtils.isEmpty(merchantStoreDTOS)) {
                return Boolean.FALSE;
            }
            queryStoreIds = merchantStoreDTOS.stream().map(MerchantStoreDTO::getId).collect(Collectors.toList());
        }
        orderAfterSaleQueryDTO.setStoreIds(queryStoreIds);
        List<Long> accountIds = new ArrayList<>();
        // 注册手机号
        if (!StringUtils.isEmpty(orderAfterSaleQueryDTO.getPhone())) {
            List<MerchantStoreAccount> merchantStoreAccounts = merchantStoreAccountService.queryMerchantStoreAccount(orderAfterSaleQueryDTO.getPhone(), loginContextInfoDTO.getTenantId());
            accountIds = merchantStoreAccounts.stream().map(MerchantStoreAccount::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(accountIds)) {
                return Boolean.FALSE;
            }
        }
        if (null != orderAfterSaleQueryDTO.getEndTime() && null != orderAfterSaleQueryDTO.getStartTime()) {
            orderAfterSaleQueryDTO.setEndTime(DateUtil.dayEnd(orderAfterSaleQueryDTO.getEndTime()));
            orderAfterSaleQueryDTO.setStartTime(DateUtil.zero(orderAfterSaleQueryDTO.getStartTime()));
        }
        orderAfterSaleQueryDTO.setAccountIds(accountIds);

        //商品条件
        orderAfterSaleQueryDTO.setItemIds(new ArrayList<>());
        if (Objects.nonNull(orderAfterSaleQueryDTO.getItemId()) || Objects.nonNull(orderAfterSaleQueryDTO.getTitle())) {
            MarketItemQueryDTO query = new MarketItemQueryDTO();
            query.setId(orderAfterSaleQueryDTO.getItemId());
            query.setTitle(orderAfterSaleQueryDTO.getTitle());
            query.setTenantId(loginContextInfoDTO.getTenantId());
            List<MarketItem> marketItems = marketItemService.listAll(query);
            if (CollectionUtils.isEmpty(marketItems)) {
                return Boolean.FALSE;
            }
            orderAfterSaleQueryDTO.setItemIds(marketItems.stream().map(MarketItem::getId).collect(Collectors.toList()));
        }

        // 仓库处理
        WarehouseQueryEnum warehouseQueryEnum = WarehouseQueryEnum.getById(orderAfterSaleQueryDTO.getWarehouseNo());
        orderAfterSaleQueryDTO.setWarehouseType(transferWarehouseType(orderAfterSaleQueryDTO, warehouseQueryEnum));
        if (Objects.nonNull(warehouseQueryEnum)) {
            orderAfterSaleQueryDTO.setWarehouseNo(null);
        }
        return Boolean.TRUE;
    }

    private Integer transferWarehouseType(OrderAfterSaleQueryDTO orderAfterSaleQueryDTO, WarehouseQueryEnum warehouseQueryEnum) {
        if (Objects.isNull(orderAfterSaleQueryDTO.getWarehouseNo())) {
            return null;
        }
        // 获取查询的仓库类型
        Integer warehouseType = WarehouseTypeEnum.PROPRIETARY.getCode();
        if (Objects.nonNull(warehouseQueryEnum)) {
            warehouseType = warehouseQueryEnum == WarehouseQueryEnum.NO_WAREHOUSE ? WarehouseTypeEnum.NO_WAREHOUSE.getCode() : warehouseType;
            warehouseType = warehouseQueryEnum == WarehouseQueryEnum.THIRD_WAREHOUSE ? WarehouseTypeEnum.THREE_PARTIES.getCode() : warehouseType;
        }
        return warehouseType;
    }

    private void populateSupplierQueryCondition(OrderAfterSaleQueryDTO orderAfterSaleQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        orderAfterSaleQueryDTO.setServiceTypes(Lists.newArrayList());
        List<Long> supplierIds = Lists.newArrayList();
//        List<Integer> supplierCareServiceTypes = Lists.newArrayList(OrderAfterSaleServiceTypeEnum.RETURN_REFUND.getType(), OrderAfterSaleServiceTypeEnum.RETURN_REFUND_ENTER_BILL.getType(), OrderAfterSaleServiceTypeEnum.RETURN_REFUND_BALANCE.getType(), OrderAfterSaleServiceTypeEnum.RESEND.getType());
        if (orderAfterSaleQueryDTO.getSupplierId() != null) {
            supplierIds.add(orderAfterSaleQueryDTO.getSupplierId());
            orderAfterSaleQueryDTO.setSupplierIds(supplierIds);
//            orderAfterSaleQueryDTO.setServiceTypes(supplierCareServiceTypes);
            return;
        }
        TenantAccount tenantAccount = tenantAccountService.selectByAuthUserId(loginContextInfoDTO.getAuthUserId());
        supplierIds = tenantAccountSupplierMappingService.queryByAccountId(tenantAccount.getId());
//        if (!CollectionUtils.isEmpty(supplierIds)) {
//            orderAfterSaleQueryDTO.setServiceTypes(supplierCareServiceTypes);
//        }
        orderAfterSaleQueryDTO.setSupplierIds(supplierIds);
    }

    @Override
    public List<BillOrderAfterSaleVO> batchQueryByOrderAfterSaleIds(List<Long> orderAfterSaleIds, Long tenantId) {
        List<OrderAfterSaleResp> orderAfterSales = orderAfterSaleQueryFacade.queryByIds(orderAfterSaleIds);
        // 获取订单Id
        Set<Long> orderIds = orderAfterSales.stream().map(OrderAfterSaleResp::getOrderId).collect(Collectors.toSet());
        List<BillOrderDTO> billOrderDTOS = orderStatisticsQueryFacade.queryOrderDetailForBill(tenantId, Lists.newArrayList(orderIds));

        List<Long> marketItemIds = billOrderDTOS.stream().map(BillOrderDTO::getItemId).collect(Collectors.toList());
        Map<Long, MarketItemDTO> marketItemMap = marketItemService.getMapByItemIds(marketItemIds);

        Map<Long, BillOrderDTO> billOrderDTOMap = billOrderDTOS.stream().collect(Collectors.toMap(BillOrderDTO::getOrderItemId, item -> item));
        List<BillOrderAfterSaleVO> billOrderAfterSaleVOS = orderAfterSales.stream().map(item -> {
            BillOrderAfterSaleVO billOrderAfterSaleVO = new BillOrderAfterSaleVO();
            BeanUtils.copyProperties(item, billOrderAfterSaleVO);
            billOrderAfterSaleVO.setOrderAfterSaleId(item.getId());
            BillOrderDTO billOrderDTO = billOrderDTOMap.get(item.getOrderItemId());
            billOrderAfterSaleVO.setOrderAfterSaleNo(item.getAfterSaleOrderNo());
            billOrderAfterSaleVO.setOrderNo(billOrderDTO.getOrderNo());
            billOrderAfterSaleVO.setOrderCreateTime(billOrderDTO.getCreateTime());
            billOrderAfterSaleVO.setOrderAfterSaleFinishedTime(item.getUpdateTime());
            billOrderAfterSaleVO.setTitle(billOrderDTO.getTitle());
            // TODO 前端显示字段 item -> skuId skuId -> supplySkuId
            billOrderAfterSaleVO.setSkuId(billOrderDTO.getItemId());
            billOrderAfterSaleVO.setSupplySkuId(billOrderDTO.getSkuId());

            billOrderAfterSaleVO.setSpecification(billOrderDTO.getSpecification());
            billOrderAfterSaleVO.setPrice(billOrderDTO.getSkuPrice());
            billOrderAfterSaleVO.setSupplyPrice(billOrderDTO.getSupplyPrice());
            billOrderAfterSaleVO.setAmount(billOrderDTO.getAmount());
            billOrderAfterSaleVO.setOrderAfterSaleAmount(item.getAmount());
            billOrderAfterSaleVO.setOrderAfterSaleType(item.getAfterSaleType());
            billOrderAfterSaleVO.setOrderAfterSaleTypeStr(OrderAfterSaleTypeEnum.getDesc(item.getAfterSaleType()));
            billOrderAfterSaleVO.setOrderAfterSaleServiceType(item.getServiceType());
            billOrderAfterSaleVO.setOrderAfterSaleServiceTypeStr(OrderAfterSaleServiceTypeEnum.getDesc(item.getServiceType()));
            billOrderAfterSaleVO.setOrderAfterSalePrice(item.getTotalPrice());
            billOrderAfterSaleVO.setOrderAfterSaleDeliveryFee(item.getDeliveryFee());
            billOrderAfterSaleVO.setMainPicture(billOrderDTO.getMainPicture());
            MarketItemDTO marketItemDTO = marketItemMap.get(billOrderDTO.getItemId());
            billOrderAfterSaleVO.setItemCode(Objects.isNull(marketItemDTO) ? "" : marketItemDTO.getItemCode());
            billOrderAfterSaleVO.setWarehouseType(billOrderDTO.getWarehouseType());
            billOrderAfterSaleVO.setResponsibilityTypeDesc(handlerResponsibilityTypeDesc(item.getResponsibilityType()));
            return billOrderAfterSaleVO;
        }).collect(Collectors.toList());

        return billOrderAfterSaleVOS;
    }

    private String handlerResponsibilityTypeDesc(Integer type) {
        for (ResponsibilityTypeEnum value : ResponsibilityTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return "";
    }


    @Override
    public List<OrderAfterSaleResp> queryBillOrderByStartTimeAndEndTime(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        QueryBillOrderAfterSaleReq req = new QueryBillOrderAfterSaleReq();
        req.setTenantId(tenantId);
        req.setStartTime(startTime);
        req.setEndTime(endTime);
        return orderAfterSaleQueryFacade.queryOrderAfterSaleForBill(req);
    }

    @Override
    public CommonResult save(OrderAfterSaleBizDTO orderAfterSaleBizDTO) {
        // 只能是已到货售后
        if (Objects.equals(orderAfterSaleBizDTO.getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "暂时不支持发起配送前售后");
        }

        Long orderItemId = orderAfterSaleBizDTO.getOrderItemId();
        OrderItemResp orderItemResp = orderItemQueryFacade.queryById(orderItemId);
        OrderResp orderResp = orderQueryFacade.queryById(orderItemResp.getOrderId());
        if (!OrderStatusEnum.ableApplyDeliveredAfterSale(orderResp.getStatus())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "该订单暂时不支持发起配送后售后");
        }

        orderAfterSaleBizDTO.setTenantId(orderItemResp.getTenantId());
        orderAfterSaleBizDTO.setWarehouseType(orderResp.getWarehouseType());
        orderAfterSaleBizDTO.setOrderId(ObjectUtil.isNotNull(orderAfterSaleBizDTO.getOrderId()) ? orderAfterSaleBizDTO.getOrderId() : orderItemResp.getOrderId());
        orderAfterSaleBizDTO.setReqSource("manage");

        if (Objects.equals(orderResp.getPayType(), com.cosfo.ordercenter.client.common.PayTypeEnum.COMBINED_PAY.getCode())) {
            List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.querySuccessCombinedByOrderId(orderResp.getTenantId(), orderResp.getId());
            List<Integer> combinedPayTypes = combinedDetails.stream().map(detail -> {
                return PaymentTradeTypeEnum.getPayTypeByTradeType(detail.getTradeType());
            }).collect(Collectors.toList());
            orderAfterSaleBizDTO.setCombinedPayTypes(combinedPayTypes);
        }

        Long recordId = null;
        try {
            OrderAfterSaleAddReq addReq = OrderAfterSaleConvert.INSTANCE.bizDto2Req(orderAfterSaleBizDTO);
            recordId = RpcResultUtil.handle(orderAfterSaleCommandProvider.createAfterDeliveryAfterSale(addReq));
        } catch (Exception e) {
            log.error("发起售后失败", e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "发起售后失败，请联系管理员");
        }
        return CommonResult.ok();
    }

//    @Override
//    public void verifyApplyPriceAndAmount(Long id, BigDecimal applyPrice, Integer applyAmount) {
//        OrderAfterSaleResp afterSale = orderAfterSaleQueryFacade.getOrderAfterSaleById(id);
//        OrderAfterSaleEnableResp orderAfterSaleEnableApplyDTO = orderAfterSaleQueryFacade.queryEnableApply(afterSale.getTenantId(), afterSale.getOrderId(), afterSale.getOrderItemId())
//                .get(afterSale.getOrderItemId());
//
//        Integer enableApplyQuantity = orderAfterSaleEnableApplyDTO.getEnableApplyQuantity();
//        BigDecimal enableApplyPrice = orderAfterSaleEnableApplyDTO.getEnableApplyPrice();
//        if (enableApplyQuantity < 0) {
//            throw new DefaultServiceException("可售后件数不足");
//        }
//        if (enableApplyPrice.compareTo(BigDecimal.ZERO) < 0) {
//            throw new DefaultServiceException("可售后金额不足,最大可售后金额为" + enableApplyPrice + "元");
//        }
//    }

    @Override
    public OrderAfterSaleRuleResultVO queryRule(Long tenantId) {
        List<OrderAfterSaleRuleResp> resps = orderAfterSaleRuleQueryFacade.queryByTenantId(tenantId);
        OrderAfterSaleRuleResultVO orderAfterSaleRuleResultVO = new OrderAfterSaleRuleResultVO();
        resps.forEach(dto -> {
            orderAfterSaleRuleResultVO.setTenantId(dto.getTenantId());
            orderAfterSaleRuleResultVO.setType(dto.getType());
            if (OrderAfterSaleRuleEnums.DefaultFlag.TRUE.getFlag().equals(dto.getDefaultFlag())) {
                OrderAfterSaleRuleDetailVO defaultRule = JSONObject.parseObject(dto.getRule(), OrderAfterSaleRuleDetailVO.class);
                orderAfterSaleRuleResultVO.setDefaultRule(defaultRule);
                orderAfterSaleRuleResultVO.setAutoFinishedTime(defaultRule.getAutoFinishedTime());
            } else if (OrderAfterSaleRuleEnums.DefaultFlag.FALSE.getFlag().equals(dto.getDefaultFlag())) {
                List<OrderAfterSaleRuleDetailVO> orderAfterSaleRuleDetailVOList = JSONObject.parseArray(dto.getRule(), OrderAfterSaleRuleDetailVO.class);
                List<Long> classificationIds = new ArrayList<>();
                orderAfterSaleRuleDetailVOList.forEach(orderAfterSaleRuleDetailVO -> {
                    if (!CollectionUtils.isEmpty(orderAfterSaleRuleDetailVO.getClassificationIds())) {
                        classificationIds.addAll(orderAfterSaleRuleDetailVO.getClassificationIds());
                    }

                    if(DeliveryTypeEnum.THIRD_DELIVERY.getCode().equals(orderAfterSaleRuleDetailVO.getDeliveryType()) && orderAfterSaleRuleDetailVO.getExpressOrderApplyEndTime() == null){
                        orderAfterSaleRuleDetailVO.setExpressOrderApplyEndTime(orderAfterSaleRuleDetailVO.getApplyEndTime());
                    }
                });

                if(!CollectionUtils.isEmpty(classificationIds)) {
                    MarketClassificationQueryReq req = new MarketClassificationQueryReq();
                    req.setTenantId(tenantId);
                    req.setClassificationIds(classificationIds);
                    List<MarketClassificationResp> marketClassificationRespList = marketClassificationFacade.queryClassificationByCondition(req);
                    Set<Long> existClassificationIds = new HashSet<>();
                    if (!CollectionUtils.isEmpty(marketClassificationRespList)) {
                        existClassificationIds = marketClassificationRespList.stream().map(MarketClassificationResp::getId).collect(Collectors.toSet());
                    }

                    Set<Long> finalExistClassificationIds = existClassificationIds;
                    orderAfterSaleRuleDetailVOList.stream()
                            .filter(orderAfterSaleRuleDetailVO -> !CollectionUtils.isEmpty(orderAfterSaleRuleDetailVO.getClassificationIds()))
                            .forEach(orderAfterSaleRuleDetailVO -> {
                                if (!CollectionUtils.isEmpty(finalExistClassificationIds)) {
                                    List<Long> result = orderAfterSaleRuleDetailVO.getClassificationIds().stream().filter(classificationId -> finalExistClassificationIds.contains(classificationId)).collect(Collectors.toList());
                                    orderAfterSaleRuleDetailVO.setClassificationIds(result);
                                } else {
                                    orderAfterSaleRuleDetailVO.setClassificationIds(Collections.emptyList());
                                }
                            });
                }
                orderAfterSaleRuleResultVO.setOrderAfterSaleRuleDetailVOList(orderAfterSaleRuleDetailVOList);
            }
        });

        TenantCommonConfigVO noWarehouseConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfig.NO_WAREHOUSE_AFTER_SALE_APPROVAL_RULE.getConfigKey());
        orderAfterSaleRuleResultVO.setNoWarehouseAfterSaleApprovalRule(Integer.valueOf(noWarehouseConfigVO.getConfigValue()));

        TenantCommonConfigVO selfWarehouseConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfig.SELF_WAREHOUSE_AFTER_SALE_APPROVAL_RULE.getConfigKey());
        orderAfterSaleRuleResultVO.setSelfWarehouseAfterSaleApprovalRule(Integer.valueOf(selfWarehouseConfigVO.getConfigValue()));

        TenantCommonConfigVO autoCreateConfigVO = tenantCommonConfigService.selectTenantConfig(tenantId, TenantConfig.AUTO_CREATE_RESEND_AFTER_SALE_RULE.getConfigKey());
        orderAfterSaleRuleResultVO.setAutoCreateResendAfterSaleRule(Integer.valueOf(autoCreateConfigVO.getConfigValue()));

        return orderAfterSaleRuleResultVO;
    }

    @Override
    public Boolean updateRule(OrderAfterSaleRuleUpdateDTO afterSaleRuleDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 查询
        List<OrderAfterSaleRuleResp> resps = orderAfterSaleRuleQueryFacade.queryByTenantId(loginContextInfoDTO.getTenantId());
        // 校验参数
        checkRuleParams(afterSaleRuleDTO);
        List<OrderAfterSaleRuleDetailDTO> orderAfterSaleRuleDetailDTOList = afterSaleRuleDTO.getOrderAfterSaleRuleDetailDTOList();
        List<OrderAfterSaleRuleCommandReq> orderAfterSaleRuleDTOS = new LinkedList<>();
        // 默认运费规则
        OrderAfterSaleRuleResp defaultOrderAfterSaleRuleVO = null;
        // 非默认
        OrderAfterSaleRuleResp orderAfterSaleRuleVO = null;

        for (OrderAfterSaleRuleResp afterSaleRuleVO : resps) {
            // 默认运费规则
            if (OrderAfterSaleRuleEnums.DefaultFlag.TRUE.getFlag().equals(afterSaleRuleVO.getDefaultFlag())) {
                defaultOrderAfterSaleRuleVO = afterSaleRuleVO;
                // 默认运费规则
                OrderAfterSaleRuleCommandReq orderAfterSaleRuleDTO = new OrderAfterSaleRuleCommandReq();
                orderAfterSaleRuleDTO.setId(afterSaleRuleVO.getId());
                orderAfterSaleRuleDTO.setTenantId(loginContextInfoDTO.getTenantId());
                orderAfterSaleRuleDTO.setType(afterSaleRuleDTO.getType());
                orderAfterSaleRuleDTO.setDefaultFlag(afterSaleRuleVO.getDefaultFlag());
                OrderAfterSaleRuleDetailDTO defaultRule = afterSaleRuleDTO.getDefaultRule();
                if (Objects.nonNull(afterSaleRuleDTO.getAutoFinishedTime())) {
                    defaultRule.setAutoFinishedTime(afterSaleRuleDTO.getAutoFinishedTime());
                }

                orderAfterSaleRuleDTO.setRule(JSON.toJSONString(defaultRule));
                orderAfterSaleRuleDTOS.add(orderAfterSaleRuleDTO);
            } else if (OrderAfterSaleRuleEnums.DefaultFlag.FALSE.getFlag().equals(afterSaleRuleVO.getDefaultFlag())) {
                orderAfterSaleRuleVO = afterSaleRuleVO;
            }
        }

        if (CollectionUtils.isEmpty(orderAfterSaleRuleDetailDTOList) && Objects.nonNull(orderAfterSaleRuleVO)) {
            // 删除非默认规则
            orderAfterSaleRuleCommandProvider.deleteRule(orderAfterSaleRuleVO.getId());
        }

        if (!CollectionUtils.isEmpty(orderAfterSaleRuleDetailDTOList) && Objects.nonNull(orderAfterSaleRuleVO)) {
            OrderAfterSaleRuleCommandReq orderAfterSaleRuleDTO = new OrderAfterSaleRuleCommandReq();
            orderAfterSaleRuleDTO.setId(orderAfterSaleRuleVO.getId());
            orderAfterSaleRuleDTO.setTenantId(loginContextInfoDTO.getTenantId());
            orderAfterSaleRuleDTO.setType(afterSaleRuleDTO.getType());
            orderAfterSaleRuleDTO.setDefaultFlag(OrderAfterSaleRuleEnums.DefaultFlag.FALSE.getFlag());
            if (Objects.nonNull(afterSaleRuleDTO.getAutoFinishedTime())) {
                orderAfterSaleRuleDetailDTOList.forEach(e -> e.setAutoFinishedTime(afterSaleRuleDTO.getAutoFinishedTime()));
            }

            orderAfterSaleRuleDTO.setRule(JSON.toJSONString(orderAfterSaleRuleDetailDTOList));
            orderAfterSaleRuleDTOS.add(orderAfterSaleRuleDTO);
        }

        if (!CollectionUtils.isEmpty(orderAfterSaleRuleDetailDTOList) && Objects.isNull(orderAfterSaleRuleVO)) {
            OrderAfterSaleRuleCommandReq orderAfterSaleRuleDTO = new OrderAfterSaleRuleCommandReq();
            orderAfterSaleRuleDTO.setTenantId(loginContextInfoDTO.getTenantId());
            orderAfterSaleRuleDTO.setType(afterSaleRuleDTO.getType());
            orderAfterSaleRuleDTO.setDefaultFlag(OrderAfterSaleRuleEnums.DefaultFlag.FALSE.getFlag());
            orderAfterSaleRuleDetailDTOList.forEach(e -> e.setAutoFinishedTime(afterSaleRuleDTO.getAutoFinishedTime()));
            orderAfterSaleRuleDTO.setRule(JSON.toJSONString(orderAfterSaleRuleDetailDTOList));
            orderAfterSaleRuleCommandProvider.add(orderAfterSaleRuleDTO);
        }
        // 删除历史
//        afterSaleRuleService.updateRule(orderAfterSaleRuleDTOS);
        for (OrderAfterSaleRuleCommandReq orderAfterSaleRuleDTO : orderAfterSaleRuleDTOS) {
            orderAfterSaleRuleCommandProvider.updateRule(orderAfterSaleRuleDTO);
        }

        // 更新售后配送前审批规则
        tenantCommonConfigService.updateTenantConfig(loginContextInfoDTO.getTenantId(),
                new TenantConfigInput(TenantConfig.NO_WAREHOUSE_AFTER_SALE_APPROVAL_RULE.getConfigKey(), String.valueOf(afterSaleRuleDTO.getNoWarehouseAfterSaleApprovalRule())));
        tenantCommonConfigService.updateTenantConfig(loginContextInfoDTO.getTenantId(),
                new TenantConfigInput(TenantConfig.SELF_WAREHOUSE_AFTER_SALE_APPROVAL_RULE.getConfigKey(), String.valueOf(afterSaleRuleDTO.getSelfWarehouseAfterSaleApprovalRule())));

        String autoCreateResendAfterSaleStr = Optional.ofNullable(afterSaleRuleDTO.getAutoCreateResendAfterSaleRule()).map(String::valueOf).orElse(TenantConfig.AUTO_CREATE_RESEND_AFTER_SALE_RULE.getDefaultValue());
        tenantCommonConfigService.updateTenantConfig(loginContextInfoDTO.getTenantId(),
                new TenantConfigInput(TenantConfig.AUTO_CREATE_RESEND_AFTER_SALE_RULE.getConfigKey(), autoCreateResendAfterSaleStr));

        return Boolean.TRUE;
    }

    /**
     * 校验参数
     *
     * @param afterSaleRuleDTO
     */
    private void checkRuleParams(OrderAfterSaleRuleUpdateDTO afterSaleRuleDTO) {
        OrderAfterSaleRuleEnums.Type type = OrderAfterSaleRuleEnums.Type.getByCode(afterSaleRuleDTO.getType());
        List<OrderAfterSaleRuleDetailDTO> orderAfterSaleRuleDetailDTOList = afterSaleRuleDTO.getOrderAfterSaleRuleDetailDTOList();
        switch (type) {
            case WAREHOUSE:
                if (!CollectionUtils.isEmpty(orderAfterSaleRuleDetailDTOList)) {
                    boolean match = orderAfterSaleRuleDetailDTOList.stream().anyMatch(e -> Objects.isNull(e.getDeliveryType()));
                    if (match) {
                        throw new BizException("仓库类型不能为空");
                    }

                    boolean applyEndTimeMatch = orderAfterSaleRuleDetailDTOList.stream().anyMatch(e -> Objects.isNull(e.getApplyEndTime()));
                    if (applyEndTimeMatch) {
                        throw new BizException("可申请售后时间不能为空");
                    }
                }

                break;
            case CLASSIFICATION:
                if (!CollectionUtils.isEmpty(orderAfterSaleRuleDetailDTOList)) {
                    boolean match = orderAfterSaleRuleDetailDTOList.stream().anyMatch(e -> CollectionUtils.isEmpty(e.getClassificationIds()));
                    if (match) {
                        throw new BizException("商品分组不能为空");
                    }

                    boolean applyEndTimeMatch = orderAfterSaleRuleDetailDTOList.stream().anyMatch(e -> Objects.isNull(e.getApplyEndTime()));
                    if (applyEndTimeMatch) {
                        throw new BizException("可申请售后时间不能为空");
                    }
                }
                break;
            case NULL_ERROR:
                throw new BizException(OrderAfterSaleRuleEnums.Type.NULL_ERROR.getDesc());
        }
    }

    @Override
    public BigDecimal calculateRefundPrice(Long orderItemId, Integer amount) {
        OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(orderItemId);
        req.setQuantity(amount);
        return orderAfterSaleQueryFacade.calculateRefundPrice(req);
    }

    @Override
    public OrderSimpleDTO queryOrderSimpleDto(String afterSaleOrderNo) {
        OrderAfterSaleResp orderAfterSale = orderAfterSaleQueryFacade.getOrderAfterSaleByNo(afterSaleOrderNo);
        AssertCheckParams.expectTrue(Objects.nonNull(orderAfterSale), ResultStatusEnum.SERVER_ERROR.getStatus(), "售后单不存在");
        OrderResp orderResp = orderQueryFacade.queryById(orderAfterSale.getOrderId());
        String orderNo = Optional.ofNullable(orderResp).map(OrderResp::getOrderNo).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
        OrderSimpleDTO orderSimpleDTO = new OrderSimpleDTO();
        orderSimpleDTO.setOrderId(orderAfterSale.getOrderId());
        orderSimpleDTO.setOrderNo(orderNo);
        return orderSimpleDTO;
    }
}
