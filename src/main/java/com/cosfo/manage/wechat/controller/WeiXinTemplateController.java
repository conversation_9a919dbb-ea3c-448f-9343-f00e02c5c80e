package com.cosfo.manage.wechat.controller;

import com.cosfo.manage.common.context.PrivacyVerEnum;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.wechat.model.vo.*;
import com.cosfo.manage.wechat.service.AuthorizerService;
import com.cosfo.manage.wechat.service.WeixinTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.io.IOException;


@Slf4j
@RestController
public class WeiXinTemplateController extends BaseController {

    @Resource
    WeixinTemplateService weixinTemplateService;
    @Resource
    AuthorizerService authorizerService;

//    /**
//     * 获取待开发小程序用户列表
//     * 通过
//     */
//    @RequestMapping(value="/template/getDevelopTenants",method = RequestMethod.GET)
//    public ResultDTO getDevelopTenants() {
//        return ResultDTO.success(weixinTemplateService.getAuthTenants());
//    }
    /**
     * 获取授权方accessToken
     * 通过
     * manage
     */
    @RequestMapping(value="/template/getAccessTokenByAppId",method = RequestMethod.GET)
    public ResultDTO getAccessTokenByAppId(@RequestParam("appId") String appId) {
        return ResultDTO.success(authorizerService.getAccessTokenByAppId(appId).getAccessToken());
    }
//    /**
//     * 获取代码模板列表
//     * 通过
//     */
//    @RequestMapping(value="/template/getTemplateList",method = RequestMethod.GET)
//    public ResultDTO getTemplateList() {
//        return ResultDTO.success(weixinTemplateService.getTemplateList());
//    }
//    /**
//     * 获取微信小程序草稿列表
//     * 通过
//     */
//    @RequestMapping(value="/template/draft/list",method = RequestMethod.GET)
//    public ResultDTO getDraftList(){
//        return ResultDTO.success(weixinTemplateService.getDraftList());
//    }
//    /**
//     * 把草稿选为模板
//     * 通过
//     */
//    @RequestMapping(value="/template/addTemplate",method = RequestMethod.POST)
//    public ResultDTO addTemplate(@RequestBody DraftVo draftVo) {
//        weixinTemplateService.addTpDraftToTemplate(draftVo.getDraftId());
//        return ResultDTO.success("SUCCESS");
//    }
//
//    /**
//     * 删除模板
//     * 通过
//     */
//    @DeleteMapping(value="/template/delTemplate")
//    public ResultDTO delTpTemplate(@RequestParam("templateId") String templateId) {
//        weixinTemplateService.delTpTemplate(templateId);
//        return ResultDTO.success("SUCCESS");
//    }
//    /**
//     * 获取体验版二维码
//     */
//    @RequestMapping(value="/template/getPreQrCode",method = RequestMethod.GET)
//    public ResultDTO getPreQrCode(@RequestParam("appId") String appId,
//                                  @RequestParam(value = "qrCodePath",required = false) String qrCodePath) throws IOException {
//        return ResultDTO.success(weixinTemplateService.getTpQrCode(appId,qrCodePath));
//    }
    /**
     * 绑定微信用户为体验者
     * 通过
     * manage
     */
    @RequestMapping(value="/template/bindTester",method = RequestMethod.POST)
    public ResultDTO bindTester(@RequestBody BindTesterVo bindTesterVo) {
        weixinTemplateService.bindTester(bindTesterVo);
        return ResultDTO.success("SUCCESS");
    }
//    /**
//     * 上传小程序代码并生成体验版
//     * 此接口不传appId为全量，传不是全量
//     * 通过
//     */
//    @RequestMapping(value="/template/commitCodeExperience",method = RequestMethod.POST)
//    public ResultDTO commitCodeExperience(@RequestBody CommitCodePreVo commitCodePreVo) throws Exception {
//        weixinTemplateService.commitCodeExperience(commitCodePreVo);
//        return ResultDTO.success("SUCCESS");
//    }

    /**
     * 查询小程序用户隐私保护指引
     * manage
     */
    @RequestMapping(value="/template/getPrivacySetting",method = RequestMethod.GET)
    public ResultDTO getPrivacySetting(@RequestParam("appId") String appId,
    @RequestParam(value = "privacyVer",required = false) Integer privacyVer) throws Exception {
        return ResultDTO.success(weixinTemplateService.getPrivacySetting(appId,1));
    }
//
//    /**
//     * 设置小程序用户隐私保护指引
//     */
//    @RequestMapping(value="/template/setPrivacySetting",method = RequestMethod.POST)
//    public ResultDTO setPrivacySetting(@RequestBody PrivateSettingVo privateSettingVo) throws Exception {
//        int ver = PrivacyVerEnum.DEV.getCode();
//        if (!ObjectUtils.isEmpty(privateSettingVo) && privateSettingVo.getPrivacyVer() == PrivacyVerEnum.ONLINE.getCode()){
//            ver = PrivacyVerEnum.ONLINE.getCode();
//        }else {
//            ver = PrivacyVerEnum.DEV.getCode();
//        }
//        weixinTemplateService.setPrivacySetting(privateSettingVo,ver);
//        return ResultDTO.success("SUCCESS");
//    }
//
//    /**
//     * 提交审核接口，此接口传appId为根据appId进行提交，不传为全量状态为开发版本的提交审核
//     * 通过
//     */
//    @RequestMapping(value="/template/submitAuditPackage",method = RequestMethod.POST)
//    public ResultDTO submitAuditPackage(@RequestBody CommitAuditVo commitAuditVo) throws Exception {
//        return  weixinTemplateService.submitAuditPackage(commitAuditVo);
//    }
//
//    /**
//     * 发布已过审的小程序，此接口传appId为根据appId进行发布，不传为全量发布（全量发布需要全部小程序通过审核）
//     * 1.更新原有审核成功的版本变为已发布，关联发布id
//     * 2.更新前查询之前生成环境版本，如果存在上个生产环境版本则把生产环境版本设置为当前的上个版本
//     * 3.更新当前的审核通过的版本为生产环境版本
//     */
//    @RequestMapping(value="/template/releasePackage",method = RequestMethod.POST)
//    public ResultDTO releasePackage(@RequestBody CommitAuditVo commitAuditVo) throws Exception {
//        return  weixinTemplateService.releasePackage(commitAuditVo);
//    }
//
//    /**
//     * 指定appId为某个appId回退，不指定为全量版本回退
//     */
//    @RequestMapping(value="/template/rollbackPackage",method = RequestMethod.POST)
//    public ResultDTO rollbackPackage(@RequestBody CommitAuditVo commitAuditVo) throws Exception {
//        return weixinTemplateService.rollbackPackage(commitAuditVo);
//    }

    /**
     * 指定appId为某个appId审核撤回，不指定为全量审核撤回
     * manage 目前后端有，前端后续会新增
     */
//    @RequestMapping(value="/template/withdrawPackage",method = RequestMethod.POST)
//    public ResultDTO withdrawPackage(@RequestBody CommitAuditVo commitAuditVo) throws Exception {
//        return weixinTemplateService.withdrawPackage(commitAuditVo);
//    }


    /**
     * 初始化小程序域名
     * manage
     */
    @GetMapping(value="/system/initDomain")
    public ResultDTO initDomain(@RequestParam(value = "appId") String appId) throws Exception {
        weixinTemplateService.initDomain(appId);
        return ResultDTO.success("success");
    }



}
