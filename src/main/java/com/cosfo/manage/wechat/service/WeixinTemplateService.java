package com.cosfo.manage.wechat.service;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.wechat.bean.wxa.QrCodeResultDto;
import com.cosfo.manage.wechat.bean.wxa.WxTemplateDTO;
import com.cosfo.manage.wechat.model.dto.DraftDto;
import com.cosfo.manage.wechat.model.dto.TenantTemplateDto;
import com.cosfo.manage.wechat.model.dto.PrivateSettingDto;
import com.cosfo.manage.wechat.model.vo.BindTesterVo;
import com.cosfo.manage.wechat.model.vo.CommitAuditVo;
import com.cosfo.manage.wechat.model.vo.CommitCodePreVo;
import com.cosfo.manage.wechat.model.vo.PrivateSettingVo;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;

import java.io.IOException;
import java.util.List;

public interface WeixinTemplateService {

//    /**
//     * 获取授权开发小程序的租户列表
//     */
//    List<TenantTemplateDto> getAuthTenants();
//
//    /**
//     * 获取代码模板列表
//     */
//    List<WxTemplateDTO> getTemplateList();
//
//    /**
//     * 获取微信小程序草稿列表
//     */
//    List<DraftDto> getDraftList();
//
//    /**
//     * 获取体验版二维码
//     */
//    byte[] getTpQrCode(String appId, String path) throws IOException;

    /**
     * 绑定微信用户为体验者
     * manage
     */
    void bindTester(BindTesterVo bindTesterVo);

//    /**
//     * 把草稿选为模板
//     */
//    void addTpDraftToTemplate(String draftId);
//
//    /**
//     * 删除模板
//     */
//    void delTpTemplate(String templateId);
//
//    /**
//     * 上传小程序代码并生成体验版
//     */
//    void commitCodeExperience(CommitCodePreVo commitCodePreVo) throws Exception;

    /**
     * 查询小程序用户隐私保护指引
     * manage
     */
    PrivateSettingDto getPrivacySetting(String appId,Integer privacyVer) throws Exception;

//    /**
//     * 设置小程序用户隐私保护指引
//     */
//    void setPrivacySetting(PrivateSettingVo privateSettingVo,int ver) throws Exception;
//
//    /**
//     * 提交审核接口，此接口传appId为根据appId进行提交，不传为全量状态为开发版本的提交审核
//     */
//    ResultDTO submitAuditPackage(CommitAuditVo commitAuditVo) throws Exception;
//
//    /**
//     * 发布已过审的小程序
//     */
//    ResultDTO releasePackage(CommitAuditVo commitAuditVo) throws Exception;
//
//    /**
//     * 版本回退
//     */
//    ResultDTO rollbackPackage(CommitAuditVo commitAuditVo) throws Exception;

//    /**
//     * 审核撤回
//     * manage
//     */
//    ResultDTO withdrawPackage(CommitAuditVo commitAuditVo) throws Exception;


    /**
     * 设置小程序服务器域名
     * manage
     */
    void setModifyDomain(String appId) throws Exception;
//
//    /**
//     * 设置小程序业务域名
//     */
//    void setModifyWebviewDomain(String appId) throws Exception;

    /**
     * 设置小程序域名
     * manage
     */
    void initDomain(String appId) throws Exception;


}
