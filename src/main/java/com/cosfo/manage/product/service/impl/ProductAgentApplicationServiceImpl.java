package com.cosfo.manage.product.service.impl;

/**
 * <AUTHOR>
 */
//@Slf4j
//@Service
//public class ProductAgentApplicationServiceImpl implements ProductAgentApplicationService {
//
//    @Resource
//    private ProductAgentApplicationRepository productAgentApplicationRepository;
//    @Resource
//    private ProductAgentApplicationItemRepository productAgentApplicationItemRepository;
//    @Resource
//    private ProductCategoryService productCategoryService;
//    @Resource
//    MqProducer mqProducer;
//    @Resource
//    private TenantService tenantService;
//    @Resource
//    private ProductSpuMapper productSpuMapper;
//    @Resource
//    private ProductSkuMapper productSkuMapper;
//    @Resource
//    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
//
//    @Override
//    public CommonResult<PageInfo<ProductAgentApplicationDTO>> listAll(ProductAgentApplicationQueryDTO productAgentApplicationQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
//        //查询申请列表
//        PageHelper.startPage(productAgentApplicationQueryDTO.getPageIndex(), productAgentApplicationQueryDTO.getPageSize());
//        List<ProductAgentApplicationDTO> productAgentApplicationDtoList = productAgentApplicationRepository.listAll(productAgentApplicationQueryDTO);
//        for (ProductAgentApplicationDTO productAgentApplicationDto : productAgentApplicationDtoList) {
//            Long categoryId = productAgentApplicationDto.getCategoryId();
//            ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(categoryId);
//            productAgentApplicationDto.setCategoryStr(categoryDTO.getCategoryStr());
//
//            // 查询申请项
//            Long id = productAgentApplicationDto.getId();
//            LambdaQueryWrapper<ProductAgentApplicationItem> itemQueryWrapper = new LambdaQueryWrapper<>();
//            itemQueryWrapper.eq(ProductAgentApplicationItem::getApplicationId, id);
//            itemQueryWrapper.eq(Objects.nonNull(productAgentApplicationQueryDTO.getStatus()), ProductAgentApplicationItem::getStatus, productAgentApplicationQueryDTO.getStatus());
//            List<ProductAgentApplicationItem> itemList = productAgentApplicationItemRepository.list(itemQueryWrapper);
//            productAgentApplicationDto.setItemList(itemList);
//        }
//        return CommonResult.ok(PageInfoHelper.createPageInfo(productAgentApplicationDtoList, productAgentApplicationQueryDTO.getPageSize()));
//    }
//
//    @Override
//    public ProductAgentApplicationDTO queryDetail(Long id) {
//        //查询代仓申请
//        ProductAgentApplication application = productAgentApplicationRepository.getById(id);
//        if (Objects.isNull(application)) {
//            return null;
//        }
//        ProductAgentApplicationDTO productAgentApplicationDTO = ProductConverter.convertToProductAgentApplicationDTO(application);
//        ProductCategoryDTO categoryDTO = productCategoryService.selectWholeCategory(application.getCategoryId());
//        productAgentApplicationDTO.setCategoryStr(categoryDTO.getCategoryStr());
//
//        //查询申请项
//        LambdaQueryWrapper<ProductAgentApplicationItem> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(ProductAgentApplicationItem::getApplicationId, id);
//        List<ProductAgentApplicationItem> itemList = productAgentApplicationItemRepository.list(queryWrapper);
//        productAgentApplicationDTO.setItemList(itemList);
//        return productAgentApplicationDTO;
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public CommonResult createApplication(ProductAgentApplicationDTO productAgentApplicationDto, LoginContextInfoDTO loginContextInfoDTO) {
//        // 检查入参
//        checkParams(productAgentApplicationDto);
//
//        // 信息入库
//        productAgentApplicationDto.setTenantId(loginContextInfoDTO.getTenantId());
//        productAgentApplicationDto.setApplicationType(ProductApplicationTypeEnum.APPLICATION.getType());
//        ProductAgentApplication productAgentApplication = ProductConverter.convertToProductAgentApplication(productAgentApplicationDto);
//        productAgentApplicationRepository.save(productAgentApplication);
//        productAgentApplicationDto.setId(productAgentApplication.getId());
//
//        List<ProductAgentApplicationItem> itemList = productAgentApplicationDto.getItemList();
//        itemList.stream().forEach(el -> {
//            el.setTenantId(loginContextInfoDTO.getTenantId());
//            el.setApplicationId(productAgentApplication.getId());
//            el.setStatus(ProductAgentItemStatusEnum.PROCESSING.getStatus());
//        });
//        productAgentApplicationItemRepository.saveBatch(itemList);
//
//        // 异步通知供应商
//        asynchronousNotifySupplier(productAgentApplicationDto);
//        log.info("租户：{}发起了代仓申请，id：{}", loginContextInfoDTO.getTenantName(), productAgentApplication.getId());
//        return CommonResult.ok();
//    }
//
//    /**
//     * 检查申请代仓入参
//     * @param productAgentApplicationDTO
//     */
//    private void checkParams(ProductAgentApplicationDTO productAgentApplicationDTO) {
//        AssertCheckParams.notNull(productAgentApplicationDTO.getTitle(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "商品名称不能为空");
//        AssertCheckParams.notNull(productAgentApplicationDTO.getCategoryId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "类目不能为空");
//        AssertCheckParams.notNull(productAgentApplicationDTO.getStorageLocation(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "储存区域不能空");
//        AssertCheckParams.notNull(productAgentApplicationDTO.getStorageTemperature(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "储藏温度不能为空");
//        AssertCheckParams.notNull(productAgentApplicationDTO.getGuaranteePeriod(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "保质期时长不能为空");
//        AssertCheckParams.notNull(productAgentApplicationDTO.getGuaranteeUnit(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "保质期单位不能为空");
//
//        List<ProductAgentApplicationItem> itemList = productAgentApplicationDTO.getItemList();
//        AssertCheckParams.notEmpty(itemList, ResultDTOEnum.PARAMETER_MISSING.getCode(), "申请项不能为空");
//        for (ProductAgentApplicationItem productAgentApplicationItem : itemList) {
//            AssertCheckParams.notNull(productAgentApplicationItem.getSpecification(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "规格名称不能为空");
//            AssertCheckParams.notNull(productAgentApplicationItem.getSpecificationUnit(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "规格单位不能为空");
//            AssertCheckParams.notNull(productAgentApplicationItem.getDomesticFlag(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "进口/国产不能为空");
//            AssertCheckParams.notNull(productAgentApplicationItem.getVolume(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "体积不能为空");
//            AssertCheckParams.notNull(productAgentApplicationItem.getWeightNum(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "重量不能为空");
//        }
//    }
//
//    /**
//     * 异步通知供应商
//     * @param productAgentApplicationDto
//     */
//    private void asynchronousNotifySupplier(ProductAgentApplicationDTO productAgentApplicationDto) {
//        // 组装消息体
//        SummerfarmProductApplicationDTO summerfarmProductApplicationDto = assembleMessage(productAgentApplicationDto);
//        SummerfarmMsgModel summerfarmMsgModel = new SummerfarmMsgModel();
//        summerfarmMsgModel.setMsgType(SummerfarmMsgType.PRODUCT_AGENT_APPLY);
//        summerfarmMsgModel.setMsgData(summerfarmProductApplicationDto);
//
//        // 异步通知供应商
//        mqProducer.send(SummerfarmMQTopic.SAAS_TO_SUMMRFARM, null, JSONObject.toJSONString(summerfarmMsgModel));
//    }
//
//    /**
//     * 组装代仓申请消息
//     * @param productAgentApplicationDto
//     * @return
//     */
//    private SummerfarmProductApplicationDTO assembleMessage(ProductAgentApplicationDTO productAgentApplicationDto) {
//        TenantDTO tenantDTO = tenantService.queryTenantById(productAgentApplicationDto.getTenantId());
//        SummerfarmProductApplicationDTO summerfarmProductApplicationDto = new SummerfarmProductApplicationDTO();
//        summerfarmProductApplicationDto.setId(productAgentApplicationDto.getId());
//        summerfarmProductApplicationDto.setAdminId(tenantDTO.getAdminId());
//        summerfarmProductApplicationDto.setTenantName(tenantDTO.getTenantName());
//        summerfarmProductApplicationDto.setTitle(productAgentApplicationDto.getTitle());
//        summerfarmProductApplicationDto.setStorageLocation(ProductSkuEnum.STORAGE_LOCATION.convertToSummerfarmLocation(productAgentApplicationDto.getStorageLocation()));
//        summerfarmProductApplicationDto.setStorageTemperature(productAgentApplicationDto.getStorageTemperature());
//        if (Objects.equals(productAgentApplicationDto.getGuaranteeUnit(), ProductSkuEnum.GUARANTEE_UNIT.YEAR.getType())) {
//            summerfarmProductApplicationDto.setGuaranteePeriod(productAgentApplicationDto.getGuaranteePeriod() * NumberConstant.TWELVE);
//            summerfarmProductApplicationDto.setGuaranteeUnit(ProductSkuEnum.GUARANTEE_UNIT.MONTH.getType());
//        } else {
//            summerfarmProductApplicationDto.setGuaranteePeriod(productAgentApplicationDto.getGuaranteePeriod());
//            summerfarmProductApplicationDto.setGuaranteeUnit(productAgentApplicationDto.getGuaranteeUnit());
//        }
//        summerfarmProductApplicationDto.setSupplierSpuId(productAgentApplicationDto.getSupplierSkuId());
//        summerfarmProductApplicationDto.setApplicationType(productAgentApplicationDto.getApplicationType());
//        summerfarmProductApplicationDto.setSupplierSpuId(productAgentApplicationDto.getSupplierSpuId());
//
//        List<ProductAgentApplicationItem> itemList = productAgentApplicationDto.getItemList();
//        List<SummerfarmProductApplicationItemDTO> summerfarmProductApplicationItemDtoList = itemList.stream().map(el -> {
//            SummerfarmProductApplicationItemDTO item = new SummerfarmProductApplicationItemDTO();
//            item.setId(el.getId());
//            item.setSpecification(el.getSpecification());
//            item.setSpecificationUnit(el.getSpecificationUnit());
//            item.setWeightNum(el.getWeightNum());
//            item.setDomesticFlag(el.getDomesticFlag());
//            item.setVolume(el.getVolume());
//            return item;
//        }).collect(Collectors.toList());
//        summerfarmProductApplicationDto.setItemList(summerfarmProductApplicationItemDtoList);
//        return summerfarmProductApplicationDto;
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public CommonResult createApplicationItem(ProductAgentApplicationItemDTO productAgentApplicationItemDto, LoginContextInfoDTO loginContextInfoDto) {
//        // 入参校验
//        checkParams(productAgentApplicationItemDto);
//
//        Long applicationId = productAgentApplicationItemDto.getApplicationId();
//        ProductAgentApplication productAgentApplication = productAgentApplicationRepository.getById(applicationId);
//        if (Objects.isNull(productAgentApplication)) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到代仓申请信息");
//        }
//
//        //入库
//        ProductAgentApplicationItem productAgentApplicationItem = ProductConverter.convertToProductAgentApplicationItem(productAgentApplicationItemDto);
//        productAgentApplicationItem.setTenantId(loginContextInfoDto.getTenantId());
//        productAgentApplicationItem.setStatus(ProductAgentItemStatusEnum.PROCESSING.getStatus());
//        productAgentApplicationItemRepository.save(productAgentApplicationItem);
//
//        ProductAgentApplicationDTO productAgentApplicationDTO = ProductConverter.convertToProductAgentApplicationDTO(productAgentApplication);
//        productAgentApplicationDTO.setTenantId(loginContextInfoDto.getTenantId());
//        productAgentApplicationDTO.setApplicationType(ProductApplicationTypeEnum.APPLICATION_ITEM.getType());
//        productAgentApplicationDTO.setItemList(Arrays.asList(productAgentApplicationItem));
//
//        // 异步通知
//        asynchronousNotifySupplier(productAgentApplicationDTO);
//        log.info("租户：{}发起了代仓项申请，id：{}", loginContextInfoDto.getTenantName(), productAgentApplicationItem.getId());
//
//        return CommonResult.ok();
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public CommonResult recreateApplicationItem(ProductAgentApplicationItemDTO productAgentApplicationItemDto, LoginContextInfoDTO loginContextInfoDto) {
//        // 入参校验
//        checkParams(productAgentApplicationItemDto);
//
//        Long id = productAgentApplicationItemDto.getId();
//        ProductAgentApplicationItem productAgentApplicationItem = productAgentApplicationItemRepository.getById(id);
//        if (Objects.isNull(productAgentApplicationItem)) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到申请项信息");
//        }
//        if (!Objects.equals(ProductAgentItemStatusEnum.FAIL.getStatus(), productAgentApplicationItem.getStatus())) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "非审核失败无法重新提交信息");
//        }
//
//        // 重置申请项
//        LambdaUpdateWrapper<ProductAgentApplicationItem> updateWrapper = new LambdaUpdateWrapper<>();
//        updateWrapper.eq(ProductAgentApplicationItem::getId, productAgentApplicationItemDto.getId());
//        updateWrapper.set(ProductAgentApplicationItem::getSpecification, productAgentApplicationItemDto.getSpecification());
//        updateWrapper.set(ProductAgentApplicationItem::getSpecificationUnit, productAgentApplicationItemDto.getSpecificationUnit());
//        updateWrapper.set(ProductAgentApplicationItem::getWeightNum, productAgentApplicationItemDto.getWeightNum());
//        updateWrapper.set(ProductAgentApplicationItem::getDomesticFlag, productAgentApplicationItemDto.getDomesticFlag());
//        updateWrapper.set(ProductAgentApplicationItem::getStatus, ProductAgentItemStatusEnum.PROCESSING.getStatus());
//        updateWrapper.set(ProductAgentApplicationItem::getRefuseReason, null);
//        updateWrapper.set(ProductAgentApplicationItem::getAuditTime, null);
//        updateWrapper.set(ProductAgentApplicationItem::getVolume, productAgentApplicationItemDto.getVolume());
//        productAgentApplicationItemRepository.update(updateWrapper);
//
//        ProductAgentApplication productAgentApplication = productAgentApplicationRepository.getById(productAgentApplicationItem.getApplicationId());
//
//        // 异步通知供应商
//        ProductAgentApplicationDTO productAgentApplicationDTO = new ProductAgentApplicationDTO();
//        productAgentApplicationDTO.setSupplierSpuId(productAgentApplication.getSupplierSpuId());
//        productAgentApplicationDTO.setTenantId(loginContextInfoDto.getTenantId());
//        productAgentApplicationDTO.setApplicationType(ProductApplicationTypeEnum.REAPPLICATION_ITEM.getType());
//        ProductAgentApplicationItem applicationItem = ProductConverter.convertToProductAgentApplicationItem(productAgentApplicationItemDto);
//        productAgentApplicationDTO.setItemList(Arrays.asList(applicationItem));
//        asynchronousNotifySupplier(productAgentApplicationDTO);
//        log.info("租户：{}重新发起了代仓项申请，id：{}", loginContextInfoDto.getTenantName(), productAgentApplicationItem.getId());
//        return CommonResult.ok();
//    }
//
//    /**
//     * 检查申请代仓入参
//     * @param productAgentApplicationItemDto
//     */
//    private void checkParams(ProductAgentApplicationItemDTO productAgentApplicationItemDto) {
//        AssertCheckParams.notNull(productAgentApplicationItemDto.getSpecification(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "规格名称不能为空");
//        AssertCheckParams.notNull(productAgentApplicationItemDto.getSpecificationUnit(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "规格单位不能为空");
//        AssertCheckParams.notNull(productAgentApplicationItemDto.getVolume(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "体积不能为空");
//        AssertCheckParams.notNull(productAgentApplicationItemDto.getWeightNum(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "重量不能为空");
//        AssertCheckParams.notNull(productAgentApplicationItemDto.getDomesticFlag(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "进口/国产不能为空");
//    }
//
//    @Override
//    public CommonResult deleteItem(Long id, LoginContextInfoDTO loginContextInfoDto) {
//        AssertCheckParams.notNull(id, ResultDTOEnum.PARAMETER_MISSING.getCode(), "id不能为空");
//        ProductAgentApplicationItem applicationItem = productAgentApplicationItemRepository.getById(id);
//        if (Objects.isNull(applicationItem)) {
//            return CommonResult.ok();
//        }
//        if (!Objects.equals(applicationItem.getStatus(), ProductAgentItemStatusEnum.FAIL.getStatus())) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "非审核失败无法删除");
//        }
//
//        productAgentApplicationItemRepository.removeById(id);
//        log.info("租户：{}删除了申请代仓项：{}", loginContextInfoDto.getTenantName(), id);
//        return CommonResult.ok();
//    }
//
//    @Override
//    public void receiveCallBackData(SummerfarmProductApplicationDTO summerfarmProductApplicationDto) {
//        // 校验回调入参
//        Integer applicationType = summerfarmProductApplicationDto.getApplicationType();
//        List<SummerfarmProductApplicationItemDTO> itemList = summerfarmProductApplicationDto.getItemList();
//        if (CollectionUtils.isEmpty(itemList)) {
//            log.error("代仓申请回调参数缺失，流程结束");
//            return;
//        }
//        // 申请代仓则写spu sku
//        if (Objects.equals(ProductApplicationTypeEnum.APPLICATION.getType(), applicationType)) {
//            Long id = summerfarmProductApplicationDto.getId();
//            Long supplierSpuId = summerfarmProductApplicationDto.getSupplierSpuId();
//            ProductAgentApplication update = new ProductAgentApplication();
//            update.setId(id);
//            update.setSupplierSpuId(supplierSpuId);
//            productAgentApplicationRepository.updateById(update);
//        }
//
//        for (SummerfarmProductApplicationItemDTO item : itemList) {
//            ProductAgentApplicationItem updateItem = new ProductAgentApplicationItem();
//            updateItem.setId(item.getId());
//            updateItem.setSupplierSkuId(item.getSupplierSkuId());
//            productAgentApplicationItemRepository.updateById(updateItem);
//        }
//        log.info("代仓申请回调处理完毕");
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public void receiveSummerfarmProductAuditResult(SummerfarmProductAuditResultDTO summerfarmProductAuditResultDTO) {
//        Long supplierSkuId = summerfarmProductAuditResultDTO.getSkuId();
//        Integer auditResult = summerfarmProductAuditResultDTO.getAuditResult();
//        String refuseReason = summerfarmProductAuditResultDTO.getRefuseReason();
//        LocalDateTime auditTime = summerfarmProductAuditResultDTO.getAuditTime();
//        if (Objects.isNull(supplierSkuId) || Objects.isNull(auditResult)) {
//            log.error("代仓申请回调参数为空，流程结束");
//            return;
//        }
//        ProductAgentItemStatusEnum[] values = ProductAgentItemStatusEnum.values();
//        if (Arrays.stream(values).noneMatch(el -> Objects.equals(el.getStatus(), auditResult))) {
//            throw new DefaultServiceException("代仓商品申请审核状态错误，抛出异常");
//        }
//
//        //查询到对应申请项
//        LambdaQueryWrapper<ProductAgentApplicationItem> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(ProductAgentApplicationItem::getSupplierSkuId, supplierSkuId);
//        ProductAgentApplicationItem productAgentApplicationItem = productAgentApplicationItemRepository.getOne(queryWrapper);
//        if (Objects.isNull(productAgentApplicationItem)) {
//            log.error("未查询到对应的代仓申请项");
//            return;
//        }
//
//        //变更申请项状态
//        ProductAgentApplicationItem update = new ProductAgentApplicationItem();
//        update.setId(productAgentApplicationItem.getId());
//        update.setStatus(auditResult);
//        update.setRefuseReason(refuseReason);
//        update.setAuditTime(auditTime);
//        productAgentApplicationItemRepository.updateById(update);
//
//        if (!Objects.equals(auditResult, ProductAgentItemStatusEnum.SUCCESS.getStatus())) {
//            log.info("代仓申请未通过，流程结束");
//            return;
//        }
//
//        //申请成功则新建货品sku、spu
//        LambdaQueryWrapper<ProductAgentApplication> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(ProductAgentApplication::getId, productAgentApplicationItem.getApplicationId());
//        ProductAgentApplication productAgentApplication = productAgentApplicationRepository.getOne(lambdaQueryWrapper);
//        Long supplierSpuId = productAgentApplication.getSupplierSpuId();
//        ProductSpu productSpu = productSpuMapper.selectByPrimaryKey(supplierSpuId);
//        ProductSpu insertSpu = new ProductSpu();
//        if (Objects.isNull(productSpu)) {
//            // 新建spu
//            insertSpu.setId(supplierSpuId);
//            insertSpu.setTenantId(SupplierTenantEnum.SUMMERFARM.getId());
//            insertSpu.setCategoryId(productAgentApplication.getCategoryId());
//            insertSpu.setBrandId(null);
//            insertSpu.setTitle(productAgentApplication.getTitle());
//            insertSpu.setSubTitle(null);
//            insertSpu.setMainPicture(null);
//            insertSpu.setDetailPicture(null);
//            insertSpu.setStorageLocation(productAgentApplication.getStorageLocation());
//            insertSpu.setStorageTemperature(productAgentApplication.getStorageTemperature());
//            insertSpu.setGuaranteePeriod(productAgentApplication.getGuaranteePeriod());
//            insertSpu.setGuaranteeUnit(productAgentApplication.getGuaranteeUnit());
//            insertSpu.setOrigin(null);
//            productSpuMapper.insert(insertSpu);
//        }
//
//        // 新建sku
//        ProductSku insertSku = new ProductSku();
//        insertSku.setId(supplierSkuId);
//        insertSku.setTenantId(SupplierTenantEnum.SUMMERFARM.getId());
//        insertSku.setSpuId(supplierSpuId);
//        insertSku.setSpecification(productAgentApplicationItem.getSpecification());
//        insertSku.setSpecificationUnit(productAgentApplicationItem.getSpecificationUnit());
//        productSkuMapper.insert(insertSku);
//
//        // 新建代仓信息
//        ProductAgentSkuMapping productAgentSkuMapping = new ProductAgentSkuMapping();
//        productAgentSkuMapping.setTenantId(productAgentApplicationItem.getTenantId());
//        productAgentSkuMapping.setAgentSkuId(supplierSkuId);
//        productAgentSkuMapping.setAgentTenantId(SupplierTenantEnum.SUMMERFARM.getId());
//        productAgentSkuMappingMapper.insert(productAgentSkuMapping);
//
//        log.info("代仓申请项：{}审核结果处理完毕", productAgentApplicationItem.getId());
//    }
//}
