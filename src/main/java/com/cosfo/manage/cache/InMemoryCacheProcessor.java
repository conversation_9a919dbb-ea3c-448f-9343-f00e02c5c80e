package com.cosfo.manage.cache;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Slf4j
public class InMemoryCacheProcessor {

    private static final Cache<String, Object> IN_MEMORY_CACHE = Caffeine.newBuilder()
        .expireAfterWrite(30, TimeUnit.MINUTES)
        .maximumSize(100).build();

    private static final AtomicLong REQUEST_COUNTER = new AtomicLong(0L);

    @Around("@annotation(com.cosfo.manage.cache.InMemoryCache)")
    public Object getResultFromCache(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String cacheKey = buildCacheKey(joinPoint, methodSignature);
        Object cachedResult = IN_MEMORY_CACHE.getIfPresent(cacheKey);
        if (null == cachedResult) {
            log.info("从底层接口获取数据:{}", cacheKey);
            cachedResult = joinPoint.proceed();
            if (null != cachedResult) {
                IN_MEMORY_CACHE.put(cacheKey, cachedResult);
            } else {
                log.warn("未找到对象, 不写入缓存:{}", cacheKey);
            }
        }
        if (REQUEST_COUNTER.getAndIncrement() % 500 == 0) {
            log.info("{}: {}触发打印缓存统计信息：{}", REQUEST_COUNTER.get(), cacheKey, IN_MEMORY_CACHE.stats());
        }
        return cachedResult;
    }

    private static String buildCacheKey(ProceedingJoinPoint joinPoint, MethodSignature methodSignature) {
        String targetServiceName = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = methodSignature.getMethod().getName();
        String[] argNames = methodSignature.getParameterNames();
        Object[] args = joinPoint.getArgs();

        if (null == argNames || argNames.length == 0 || args == null || args.length == 0) {
            log.warn("Invalid args or args-value:{}, {}", JSON.toJSONString(argNames), JSON.toJSONString(args));
            return String.format("%s.%s", targetServiceName, methodName);
        }
        StringBuilder argValues = new StringBuilder();
        for (int index = 0; index < argNames.length; index++) {
            String argName = argNames[index];
            if (index > 0) {
                argValues.append(";");
            }
            argValues.append(argName).append("=").append(args.length >= index ? args[index] : "null");
        }
        return String.format("%s.%s:%s", targetServiceName, methodName, argValues);
    }
}
