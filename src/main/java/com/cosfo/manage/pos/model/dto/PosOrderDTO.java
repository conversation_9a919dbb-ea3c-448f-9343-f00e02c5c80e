package com.cosfo.manage.pos.model.dto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class PosOrderDTO implements Serializable {
    private Long id;

    private Long tenantId;

    /**
     * 1=美团
     */
    private Integer channelType;

    /**
     * 外部系统门店code
     */
    private String outStoreCode;

    /**
     * 外部系统门店名称
     */
    private String outStoreName;

    /**
     * 帆台门店code
     */
    private String merchantStoreCode;

    /**
     * 外部订单编码
     */
    private String orderNo;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 优惠金额
     */
    private BigDecimal discountPrice;

    /**
     * 不含税金额
     */
    private BigDecimal excludeTaxPrice;

    /**
     * 备注
     */
    private String remarks;
    /**
     * 详情 json
     */
    private String detailInfo;

    /**
     * 生效日
     */
    private LocalDate availableDate;

    /**
     * 子订单
     */
    List<PosOrderItemDTO> orderItemDTOList;
}
