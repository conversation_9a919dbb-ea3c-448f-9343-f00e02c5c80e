package com.cosfo.manage.report.service;

import com.cosfo.manage.report.model.dto.PurchaseSummarySkuQueryDTO;
import com.cosfo.manage.report.model.dto.PurchaseSummarySupplierQueryDTO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySkuVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierAggVO;
import com.cosfo.manage.report.model.vo.PurchaseSummarySupplierVO;
import com.github.pagehelper.PageInfo;

public interface PurchaseSummaryReportService {


    /**
     * 采购汇总 - 商品维度 合计
     *
     * @param purchaseSummarySkuQueryDTO
     * @return
     */
    PurchaseSummarySkuAggVO queryPurchaseSummaryAggBySku(PurchaseSummarySkuQueryDTO purchaseSummarySkuQueryDTO);

    /**
     * 采购汇总 - 商品维度 列表
     *
     * @param purchaseSummarySkuQueryDTO
     * @return
     */
    PageInfo<PurchaseSummarySkuVO> queryPurchaseSummaryListBySku(PurchaseSummarySkuQueryDTO purchaseSummarySkuQueryDTO);

    /**
     * 采购汇总 - 商品维度 导出
     *
     * @param purchaseSummarySkuQueryDTO
     * @return
     */
    Long exportSku(PurchaseSummarySkuQueryDTO purchaseSummarySkuQueryDTO);

    /**
     * 采购汇总 - 供应商维度 合计
     *
     * @param purchaseSummarySupplierQueryDTO
     * @return
     */
    PurchaseSummarySupplierAggVO queryPurchaseSummaryAggBySupplier(PurchaseSummarySupplierQueryDTO purchaseSummarySupplierQueryDTO);

    /**
     * 采购汇总 - 供应商维度 列表
     *
     * @param purchaseSummarySupplierQueryDTO
     * @return
     */
    PageInfo<PurchaseSummarySupplierVO> queryPurchaseSummaryListBySupplier(PurchaseSummarySupplierQueryDTO purchaseSummarySupplierQueryDTO);

    /**
     * 采购汇总 - 供应商维度 导出
     *
     * @param purchaseSummarySupplierQueryDTO
     * @return
     */
    Long exportSupplier(PurchaseSummarySupplierQueryDTO purchaseSummarySupplierQueryDTO);
}
