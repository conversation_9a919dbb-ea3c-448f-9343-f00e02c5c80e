package com.cosfo.manage.report.converter;

import com.cosfo.manage.facade.input.ProductStockChangeRecordQueryInput;
import com.cosfo.manage.product.model.dto.ProductStockChangeRecordQueryDTO;
import com.cosfo.manage.product.model.vo.ProductStockChangeRecordVO;
import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.saleinventory.dto.dto.SkuQuantityChangeRecordDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/27 13:51
 */
public class Convert {
    public static ProductStockChangeRecordQueryInput productStockChangeRecord2Input(ProductStockChangeRecordQueryDTO productStockChangeRecordQueryVO) {

        if (productStockChangeRecordQueryVO == null) {
            return null;
        }
        ProductStockChangeRecordQueryInput productStockChangeRecordQueryInput = new ProductStockChangeRecordQueryInput();
        productStockChangeRecordQueryInput.setPageIndex(productStockChangeRecordQueryVO.getPageIndex());
        productStockChangeRecordQueryInput.setPageSize(productStockChangeRecordQueryVO.getPageSize());
        productStockChangeRecordQueryInput.setSkuId(productStockChangeRecordQueryVO.getSkuId());
        productStockChangeRecordQueryInput.setWarehouseNo(productStockChangeRecordQueryVO.getWarehouseNo());
        productStockChangeRecordQueryInput.setPdName(productStockChangeRecordQueryVO.getPdName());
        productStockChangeRecordQueryInput.setStockTypeList(productStockChangeRecordQueryVO.getStockTypeList());
        productStockChangeRecordQueryInput.setChangeTypeNameList(productStockChangeRecordQueryVO.getChangeTypeNameList());
        productStockChangeRecordQueryInput.setStartTime(productStockChangeRecordQueryVO.getStartTime());
        productStockChangeRecordQueryInput.setEndTime(productStockChangeRecordQueryVO.getEndTime());
        productStockChangeRecordQueryInput.setPermissionSkuIdList(productStockChangeRecordQueryVO.getPermissionSkuIdList());
        return productStockChangeRecordQueryInput;
    }

    public static List<ProductStockChangeRecordVO> productQuantityChangeRecordDTO2VO(List<SkuQuantityChangeRecordDTO> recordDTOList) {

        if (recordDTOList == null) {
            return Collections.emptyList();
        }
        List<ProductStockChangeRecordVO> productStockChangeRecordVOList = new ArrayList<>();
        for (SkuQuantityChangeRecordDTO skuQuantityChangeRecordDTO : recordDTOList) {
            productStockChangeRecordVOList.add(toProductStockChangeRecordVO(skuQuantityChangeRecordDTO));
        }
        return productStockChangeRecordVOList;
    }

    public static ProductStockChangeRecordVO toProductStockChangeRecordVO(SkuQuantityChangeRecordDTO skuQuantityChangeRecordDTO) {
        if (skuQuantityChangeRecordDTO == null) {
            return null;
        }
        ProductStockChangeRecordVO productStockChangeRecordVO = new ProductStockChangeRecordVO();
        productStockChangeRecordVO.setSkuId(skuQuantityChangeRecordDTO.getSkuId());
        productStockChangeRecordVO.setSku(skuQuantityChangeRecordDTO.getSku());
        productStockChangeRecordVO.setPdName(skuQuantityChangeRecordDTO.getPdName());
        productStockChangeRecordVO.setWarehouseNo(skuQuantityChangeRecordDTO.getWarehouseNo());
        productStockChangeRecordVO.setWarehouseName(skuQuantityChangeRecordDTO.getWarehouseName());
        productStockChangeRecordVO.setSpec(skuQuantityChangeRecordDTO.getSpec());
        productStockChangeRecordVO.setQuantity(skuQuantityChangeRecordDTO.getQuantity());
        productStockChangeRecordVO.setLockQuantity(skuQuantityChangeRecordDTO.getLockQuantity());
        productStockChangeRecordVO.setRoadQuantity(skuQuantityChangeRecordDTO.getRoadQuantity());
        productStockChangeRecordVO.setSafeQuantity(skuQuantityChangeRecordDTO.getSafeQuantity());
        productStockChangeRecordVO.setOnlineQuantity(skuQuantityChangeRecordDTO.getOnlineQuantity());
        productStockChangeRecordVO.setChangeTypeDesc(skuQuantityChangeRecordDTO.getChangeTypeDesc());
        productStockChangeRecordVO.setChangeQuantityList(skuQuantityChangeRecordDTO.getChangeQuantityList());
        productStockChangeRecordVO.setRecorder(skuQuantityChangeRecordDTO.getRecorder());
        productStockChangeRecordVO.setRecordTime(skuQuantityChangeRecordDTO.getRecordTime());
        productStockChangeRecordVO.setRemark(skuQuantityChangeRecordDTO.getRemark());
        return productStockChangeRecordVO;
    }

    public static PageInfo<ProductStockChangeRecordVO> pageInfoSkuQuantityChangeRecordDTO2VO(PageInfo<SkuQuantityChangeRecordDTO> respPageInfo) {
        if (Objects.isNull(respPageInfo)) {
            return null;
        }
        PageInfo<ProductStockChangeRecordVO> pageInfo = new PageInfo<>();
        pageInfo.setSize(respPageInfo.getSize());
        pageInfo.setPageSize(respPageInfo.getPageSize());
        pageInfo.setPageNum(respPageInfo.getPageNum());
        pageInfo.setTotal(respPageInfo.getTotal());
        List<SkuQuantityChangeRecordDTO> recordDTOList = respPageInfo.getList();
        if (!CollectionUtils.isEmpty(recordDTOList)) {
            List<ProductStockChangeRecordVO> records = Convert.productQuantityChangeRecordDTO2VO(recordDTOList);
            pageInfo.setList(records);
        }
        return pageInfo;
    }

}
