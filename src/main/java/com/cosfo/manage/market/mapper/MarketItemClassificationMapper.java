package com.cosfo.manage.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.market.model.po.MarketItemClassification;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 商品分类关联表(MarketItemClassification)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-10 17:29:35
 */
@Deprecated
@Repository
@Mapper
public interface MarketItemClassificationMapper extends BaseMapper<MarketItemClassification> {
//
//    /**
//     * 通过ID查询单条数据
//     *
//     * @param id
//     * @return 实例对象
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    MarketItemClassification selectByPrimaryKey(Long id);
//
//
//    /**
//     * 新增数据
//     *
//     * @param marketItemClassification 实例对象
//     * @return 影响行数
//     */
//    int insert(MarketItemClassification marketItemClassification);
//
//
//
//    /**
//     * 修改数据
//     *
//     * @param marketItemClassification 实例对象
//     * @return 影响行数
//     */
//    int updateByPrimaryKey(MarketItemClassification marketItemClassification);
//
//    /**
//     * 通过主键删除数据
//     *
//     * @param id
//     * @return 影响行数
//     */
//    int deleteById(Long id);
//
//    /**
//     * 查询所有
//     * @param queryClassification
//     * @return
//     */
//    List<MarketItemClassification> listAll(MarketItemClassification queryClassification);
//
//    /**
//     * 根据itemId查询
//     * @param tenantId
//     * @param marketId
//     * @return
//     */
//    @InterceptorIgnore(tenantLine = "on")
//    MarketItemClassification selectByItemId(@Param("tenantId") Long tenantId, @Param("marketId") Long marketId);
}

