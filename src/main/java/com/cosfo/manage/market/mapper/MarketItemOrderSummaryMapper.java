package com.cosfo.manage.market.mapper;

import com.cosfo.manage.market.model.po.MarketItemOrderSummary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.manage.market.model.vo.MarketItemSalesVO;
import com.cosfo.manage.report.model.dto.MarketItemSalesRankingExcelDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * item下单汇总 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Mapper
public interface MarketItemOrderSummaryMapper extends BaseMapper<MarketItemOrderSummary> {

    /**
     * 查询商品销量榜 - 按金额
     *
     * @param tenantId  租户id
     * @param startDate 开始日期
     * @param limit     查询数量
     */
    List<MarketItemSalesVO> queryMarketItemSalesRankingByAmount(@Param("tenantId") Long tenantId,
                                                                @Param("startDate") LocalDate startDate,
                                                                @Param("limit") Integer limit);

    /**
     * 查询商品销量榜 - 按数量
     *
     * @param tenantId  租户id
     * @param startDate 开始日期
     * @param limit     查询数量
     */
    List<MarketItemSalesVO> queryMarketItemSalesRankingByQuantity(@Param("tenantId") Long tenantId,
                                                                  @Param("startDate") LocalDate startDate,
                                                                  @Param("limit") Integer limit);

    /**
     * 查询商品销量榜
     *
     * @param tenantId  租户id
     * @param startDate 开始日期
     * @param orderType 排序类型
     */
    List<MarketItemSalesVO> queryMarketItemSalesRanking(@Param("tenantId") Long tenantId,
                                                        @Param("startDate") LocalDate startDate,
                                                        @Param("orderType") String orderType);


    /**
     * 批量插入
     *
     * @param marketItemOrderSummaries item下单汇总
     */
    void insertBatch(List<MarketItemOrderSummary> marketItemOrderSummaries);

}
