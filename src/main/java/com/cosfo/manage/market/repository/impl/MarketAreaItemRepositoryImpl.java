package com.cosfo.manage.market.repository.impl;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/13
 */
// 商品中心迁移，只使用到了batchUpdateOnSale方法
//@Service
//public class MarketAreaItemRepositoryImpl extends ServiceImpl<MarketAreaItemMapper, MarketAreaItem> implements MarketAreaItemRepository {
//
//    @Override
//    public MarketAreaItem selectByTenantIdAndItemId(Long tenantId, Long itemId) {
//        LambdaQueryWrapper<MarketAreaItem> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(MarketAreaItem::getItemId, itemId);
//        queryWrapper.eq(MarketAreaItem::getTenantId, tenantId);
//        return getOne(queryWrapper);
//    }
//
//    @Override
//    public List<MarketAreaItem> selectByTenantIdAndItemIds(Long tenantId, List<Long> itemIds) {
//        LambdaQueryWrapper<MarketAreaItem> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.in(MarketAreaItem::getItemId, itemIds);
//        queryWrapper.eq(MarketAreaItem::getTenantId, tenantId);
//        return list(queryWrapper);
//    }
//
//    @Override
//    public void batchUpdateOnSale(List<Long> itemIds, Integer onSale,Long tenantId) {
//        if (CollectionUtils.isEmpty(itemIds)){
//            return;
//        }
//        // 双写是一个临时性方案，暂时不考虑数据一致性。
//        List<MarketAreaItem> marketAreaItems = selectByTenantIdAndItemIds(tenantId, itemIds);
//        if (CollectionUtils.isNotEmpty(marketAreaItems)) {
//            List<Long> ids = marketAreaItems.stream().map(MarketAreaItem::getId).collect(Collectors.toList());
//            LambdaUpdateWrapper<MarketAreaItem> updateWrapper = new LambdaUpdateWrapper<>();
//            updateWrapper.set(MarketAreaItem::getOnSale,onSale);
//            updateWrapper.in(MarketAreaItem::getId,ids);
//            update(updateWrapper);
//        }
//    }
//}
