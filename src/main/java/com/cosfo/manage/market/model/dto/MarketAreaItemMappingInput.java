package com.cosfo.manage.market.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/17
 */
@Data
public class MarketAreaItemMappingInput implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 门店价格类型 0,统一价1其他价
     */
    private Integer storePriceType;

    /**
     * 价格类型：1-默认价格 2-指定分组 3-指定门店
     */
    private Integer priceType;

    /**
     * 0、百分比上浮 1、定额上浮 2、固定价 3、组合百分比下调4、组合定额下调
     */
    private Integer type;

    /**
     * 配置数额
     */
    private BigDecimal mappingNumber;

    /**
     * 门店Ids
     */
    private List<Long> storeIds;

    /**
     * 门店分组Ids
     */
    private List<Long> storeGroupIds;

    /**
     * 备注
     */
    private String remark;
    /**
     * 阶梯价
     * eg：[{"price":197.14,"unit":1}]
     */
    private List<LadderPriceDTO> ladderPrices;
}
