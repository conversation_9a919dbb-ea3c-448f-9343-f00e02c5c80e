package com.cosfo.manage.common.util.binlog.impl;

import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.context.binlog.DBTableName;
import com.cosfo.manage.common.util.binlog.TableFieldKeys;
import com.cosfo.manage.common.util.binlog.observer.BinlogObserver;
import com.cosfo.manage.common.util.binlog.observer.marketitem.MarketItemObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MarketItemDmlServiceImpl extends DbTableDmlServiceImpl {

    @Resource
    private List<MarketItemObserver> marketItemObservers;

    @Override
    public String getTableDmlName() {
        return DBTableName.COSFO_TABLE_MARKET_ITEM;
    }


    @Override
    protected List<? extends BinlogObserver> getObservers() {
        return marketItemObservers;
    }
}
