package com.cosfo.manage.common.converter;

import com.cosfo.summerfarm.model.input.SummerfarmAgentSkuWarehouseDataInput;
import net.summerfarm.manage.client.saas.req.SummerFarmAgentSkuWarehouseDataReq;
import net.summerfarm.wms.saleinventory.dto.req.QueryInventoryPageRequest;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface SaleInventoryReqMapper {
    SaleInventoryReqMapper INSTANCE = Mappers.getMapper(SaleInventoryReqMapper.class);

    QueryInventoryPageRequest inputToReq(SummerfarmAgentSkuWarehouseDataInput input);
}
