package com.cosfo.manage.common.result;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/6  11:11
 */
@Data
@NoArgsConstructor
public class PageResultDTO<D> extends ResultDTO<D> {
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 分页大小
     */
    private Integer pageSize;
    /**
     * 总条数
     */
    private Long total;
    /**
     * 总页数
     */
    private Integer pages;

    public PageResultDTO(ResultDTOEnum resultDTOEnum, D data, Integer pageNum, Integer pageSize, Long total, Integer pages) {
        super(resultDTOEnum, data);
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.pages = pages;
    }
}