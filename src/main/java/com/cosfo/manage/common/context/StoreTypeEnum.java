package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 门店类型枚举
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/20
 */
@Getter
@AllArgsConstructor
public enum StoreTypeEnum {
    //直营店，加盟店，托管店
    DIRECT(0,"直营店"),
    FRANCHISE(1,"加盟店"),
    HOSTING(2,"托管店");

    /**
     * 门店类型编码
     */
    private Integer code;
    /**
     * 门店类型描述
     */
    private String desc;

    public static String getDesc(Integer code) {
        for (StoreTypeEnum storeTypeEnum : StoreTypeEnum.values()) {
            if (storeTypeEnum.code.equals(code)) {
                return storeTypeEnum.desc;
            }
        }

        return null;
    }

    public static StoreTypeEnum getByCode(Integer code) {
        for (StoreTypeEnum storeTypeEnum : StoreTypeEnum.values()) {
            if (storeTypeEnum.code.equals(code)) {
                return storeTypeEnum;
            }
        }

        return null;
    }
}
