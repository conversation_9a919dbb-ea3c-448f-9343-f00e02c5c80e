package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TimeDurationEnum {

    TODAY(1, "今日"),
    LAST_7_DAYS(7, "近7日"),
    LAST_30_DAYS(30, "近30日");

    private final Integer duration;
    private final String desc;

    // Static map to store the mappings
    private static final Map<Integer, TimeDurationEnum> DURATION_TO_ENUM = new HashMap<>();

    // Static block to populate the map
    static {
        for (TimeDurationEnum time : TimeDurationEnum.values()) {
            DURATION_TO_ENUM.put(time.duration, time);
        }
    }

    public static TimeDurationEnum fromDuration(Integer duration) {
        TimeDurationEnum result = DURATION_TO_ENUM.get(duration);
        if (result == null) {
            throw new IllegalArgumentException("No enum constant for duration: " + duration);
        }
        return result;
    }
}
