package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/6/13 14:35
 * @Description:  spu下所有sku代仓申请状态 1-该spu下至少有一个sku申请中 2-该spu下至少有一个sku申请通过 3-该spu下所有sku未申请代仓/申请被拒
 */
@Getter
@AllArgsConstructor
public enum SpuAgentStatusEnum {
    /**
     * 该spu下至少有一个sku申请中
     */
    APPLYING(1,"该spu下至少有一个sku申请中"),
    /**
     * 该spu下至少有一个sku申请通过
     */
    APPROVED(2,"该spu下至少有一个sku申请通过"),
    /**
     * 该spu下所有sku未申请代仓/申请被拒
     */
    NO_AGENT(3,"该spu下所有sku未申请代仓/申请被拒");

    private final Integer code;
    private final String desc;

    public static Integer getCodeBySkus(List<Integer> skuAgentCodes){
        if (skuAgentCodes.contains(SkuAgentStatusEnum.APPLYING.getCode())){
            return SpuAgentStatusEnum.APPLYING.getCode();
        }else if (skuAgentCodes.contains(SkuAgentStatusEnum.APPROVED.getCode())){
            return SpuAgentStatusEnum.APPROVED.getCode();
        }else {
            return SpuAgentStatusEnum.NO_AGENT.getCode();
        }
    }

    public static SpuAgentStatusEnum getByCode(Integer code) {
        for (SpuAgentStatusEnum statusEnum : SpuAgentStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
