<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.bill.mapper.BillProfitSharingSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.manage.bill.model.po.BillProfitSharingSnapshot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="delivery_type" jdbcType="TINYINT" property="deliveryType" />
    <result column="profit_sharing_type" jdbcType="TINYINT" property="profitSharingType" />
    <result column="mapping_type" jdbcType="TINYINT" property="mappingType" />
    <result column="number" jdbcType="DECIMAL" property="number" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="profit_sharing_price" jdbcType="DECIMAL" property="profitSharingPrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="profit_sharing_no" jdbcType="VARCHAR" property="profitSharingNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, order_id, account_id, delivery_type, profit_sharing_type, mapping_type,
    `number`, origin_price, profit_sharing_price, create_time, update_time, profit_sharing_no, account_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_profit_sharing_snapshot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_profit_sharing_snapshot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharingSnapshot" useGeneratedKeys="true">
    insert into bill_profit_sharing_snapshot (tenant_id, order_id, account_id,
      delivery_type, profit_sharing_type, mapping_type,
      `number`, origin_price, profit_sharing_price,
      create_time, update_time)
    values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT},
      #{deliveryType,jdbcType=TINYINT}, #{profitSharingType,jdbcType=TINYINT}, #{mappingType,jdbcType=TINYINT},
      #{number,jdbcType=DECIMAL}, #{originPrice,jdbcType=DECIMAL}, #{profitSharingPrice,jdbcType=DECIMAL},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharingSnapshot" useGeneratedKeys="true">
    insert into bill_profit_sharing_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="profitSharingType != null">
        profit_sharing_type,
      </if>
      <if test="mappingType != null">
        mapping_type,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="originPrice != null">
        origin_price,
      </if>
      <if test="profitSharingPrice != null">
        profit_sharing_price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=TINYINT},
      </if>
      <if test="profitSharingType != null">
        #{profitSharingType,jdbcType=TINYINT},
      </if>
      <if test="mappingType != null">
        #{mappingType,jdbcType=TINYINT},
      </if>
      <if test="number != null">
        #{number,jdbcType=DECIMAL},
      </if>
      <if test="originPrice != null">
        #{originPrice,jdbcType=DECIMAL},
      </if>
      <if test="profitSharingPrice != null">
        #{profitSharingPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharingSnapshot">
    update bill_profit_sharing_snapshot
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType,jdbcType=TINYINT},
      </if>
      <if test="profitSharingType != null">
        profit_sharing_type = #{profitSharingType,jdbcType=TINYINT},
      </if>
      <if test="mappingType != null">
        mapping_type = #{mappingType,jdbcType=TINYINT},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=DECIMAL},
      </if>
      <if test="originPrice != null">
        origin_price = #{originPrice,jdbcType=DECIMAL},
      </if>
      <if test="profitSharingPrice != null">
        profit_sharing_price = #{profitSharingPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.manage.bill.model.po.BillProfitSharingSnapshot">
    update bill_profit_sharing_snapshot
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      delivery_type = #{deliveryType,jdbcType=TINYINT},
      profit_sharing_type = #{profitSharingType,jdbcType=TINYINT},
      mapping_type = #{mappingType,jdbcType=TINYINT},
      `number` = #{number,jdbcType=DECIMAL},
      origin_price = #{originPrice,jdbcType=DECIMAL},
      profit_sharing_price = #{profitSharingPrice,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryTenantProfitSharingDetail" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_snapshot
    where tenant_id = #{tenantId}
    and account_id = #{tenantId}
    and order_id = #{orderId}
  </select>

  <select id="selectByOrderIdAndReceiverTenantId"
    resultType="com.cosfo.manage.bill.model.po.BillProfitSharingSnapshot">
    select
    <include refid="Base_Column_List"/>
    from bill_profit_sharing_snapshot
    where order_id = #{orderId,jdbcType=BIGINT}
    and account_id = #{receiverTenantId,jdbcType=BIGINT}
    and profit_sharing_type = 4
  </select>

  <select id="selectByOrderIdListAndReceiverTenantId"
    resultType="com.cosfo.manage.bill.model.po.BillProfitSharingSnapshot">
    select
    <include refid="Base_Column_List"/>
    from bill_profit_sharing_snapshot
    where account_id = #{receiverTenantId,jdbcType=BIGINT}
      and order_id in(
      <foreach collection="orderIds" item="ord" separator=",">
        #{ord}
      </foreach>)
      and profit_sharing_type = 4
  </select>



  <select id="queryByTenantIdAndOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_snapshot
    where
    tenant_id = #{tenantId}
    and order_id = #{orderId}
  </select>

  <select id="queryServiceChargeByTenantIdAndOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_snapshot
    where
    tenant_id = #{tenantId}
    and order_id in
    <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
      #{orderId}
    </foreach>
    and profit_sharing_type = 4
  </select>

  <select id="queryAccountDetailByTenantIdAndOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing_snapshot
    where
    tenant_id = #{tenantId}
    and order_id in
    <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
      #{orderId}
    </foreach>
    and profit_sharing_price is not null
    and profit_sharing_price > 0
  </select>
</mapper>